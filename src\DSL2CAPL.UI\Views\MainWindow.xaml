<Window x:Class="DSL2CAPL.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="clr-namespace:DSL2CAPL.UI.Controls"
        mc:Ignorable="d"
        Title="{Binding WindowTitle}"
        Height="800"
        Width="1200"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        FontSize="13"
        Background="White"
        FontFamily="{StaticResource UIFont}">

    <Window.Resources>
        <Style x:Key="MenuItemStyle" TargetType="MenuItem">
            <Setter Property="Padding" Value="8,4" />
            <Setter Property="Margin" Value="2" />
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="LightGray">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建(_N)" Command="{Binding NewFileCommand}" InputGestureText="Ctrl+N" />
                <MenuItem Header="打开(_O)" Command="{Binding OpenFileCommand}" InputGestureText="Ctrl+O" />
                <MenuItem Header="保存(_S)" Command="{Binding SaveFileCommand}" InputGestureText="Ctrl+S" />
                <MenuItem Header="另存为(_A)" Command="{Binding SaveAsFileCommand}" InputGestureText="Ctrl+Shift+S" />
                <Separator />
                <MenuItem Header="退出(_X)" Command="{Binding ExitCommand}" InputGestureText="Alt+F4" />
            </MenuItem>

            <MenuItem Header="转换(_C)">
                <MenuItem Header="DSL转CAPL(_D)" Command="{Binding DslToCaplCommand}" InputGestureText="F5" />
                <MenuItem Header="验证DSL(_V)" Command="{Binding ValidateDslCommand}" InputGestureText="F7" />
            </MenuItem>

            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Command="{Binding AboutCommand}" />
            </MenuItem>
        </Menu>

        <!-- 工具栏 -->
        <ToolBarTray Grid.Row="1" Background="LightGray">
            <ToolBar>
                <Button Command="{Binding NewFileCommand}" Content="新建" ToolTip="新建 (Ctrl+N)" />
                <Button Command="{Binding OpenFileCommand}" Content="打开" ToolTip="打开 (Ctrl+O)" />
                <Button Command="{Binding SaveFileCommand}" Content="保存" ToolTip="保存 (Ctrl+S)" />
                <Separator />
                <Button Command="{Binding DslToCaplCommand}" Content="DSL转CAPL" ToolTip="DSL转CAPL (F5)"
                        Background="LightBlue" />
                <Button Command="{Binding ValidateDslCommand}" Content="验证DSL" ToolTip="验证DSL (F7)" />
            </ToolBar>
        </ToolBarTray>

        <!-- 主内容区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!-- 左侧编辑器 -->
            <Border Grid.Column="0" Margin="8" BorderBrush="Gray" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="LightBlue" Padding="8">
                        <TextBlock Text="DSL编辑器" FontWeight="Bold" />
                    </Border>

                    <!-- DSL编辑器 -->
                    <Border Grid.Row="1" Background="White" Margin="4">
                        <TextBox x:Name="DslTextBox"
                                 Text="{Binding DslContent, UpdateSourceTrigger=PropertyChanged}"
                                 FontFamily="{StaticResource CodeFont}"
                                 FontSize="12"
                                 AcceptsReturn="True"
                                 AcceptsTab="True"
                                 VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Auto"
                                 TextWrapping="NoWrap"
                                 Background="White"
                                 Foreground="Black" />
                    </Border>
                </Grid>
            </Border>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1"
                          HorizontalAlignment="Stretch"
                          Background="Gray" />

            <!-- 右侧预览 -->
            <Border Grid.Column="2" Margin="8" BorderBrush="Gray" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="LightGreen" Padding="8">
                        <TextBlock Text="CAPL预览" FontWeight="Bold" />
                    </Border>

                    <!-- CAPL预览区域 -->
                    <Border Grid.Row="1" Background="White" Margin="4">
                        <TextBox Text="{Binding CaplContent, Mode=OneWay}"
                                 FontFamily="{StaticResource CodeFont}"
                                 FontSize="12"
                                 IsReadOnly="True"
                                 VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Auto"
                                 TextWrapping="NoWrap" />
                    </Border>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Background="LightGray">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding StatusMessage}" />
                    <ProgressBar Width="100" Height="16" Margin="10,0,0,0"
                                 Visibility="{Binding IsProcessing, Converter={StaticResource BooleanToVisibilityConverter}}"
                                 IsIndeterminate="True" />
                </StackPanel>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="行: " />
                    <TextBlock Text="{Binding CurrentLine}" />
                    <TextBlock Text=" 列: " Margin="8,0,0,0" />
                    <TextBlock Text="{Binding CurrentColumn}" />
                    <TextBlock Text=" | " Margin="8,0,8,0" />
                    <TextBlock Text="{Binding FileSize}" />
                    <TextBlock Text=" | " Margin="8,0,8,0" />
                    <TextBlock Text="{Binding ConversionTime}" />
                </StackPanel>
            </StatusBarItem>
        </StatusBar>

        <!-- 错误提示覆盖层 -->
        <Border Grid.Row="0" Grid.RowSpan="4"
                Background="Black" Opacity="0.5"
                Visibility="{Binding ShowErrorDialog, Converter={StaticResource BooleanToVisibilityConverter}}" />

        <Border Grid.Row="0" Grid.RowSpan="4"
                Background="White"
                BorderBrush="Red" BorderThickness="2"
                CornerRadius="5"
                MaxWidth="500" MaxHeight="300"
                HorizontalAlignment="Center" VerticalAlignment="Center"
                Visibility="{Binding ShowErrorDialog, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="❌" FontSize="16" Margin="0,0,10,0" />
                    <TextBlock Text="转换错误" FontSize="16" FontWeight="Bold" Foreground="Red" />
                </StackPanel>

                <ScrollViewer Grid.Row="1" MaxHeight="150" VerticalScrollBarVisibility="Auto">
                    <TextBlock Text="{Binding ErrorMessage}" TextWrapping="Wrap" />
                </ScrollViewer>

                <Button Grid.Row="2" Content="确定" Width="80" Height="30"
                        HorizontalAlignment="Right" Margin="0,10,0,0"
                        Command="{Binding CloseErrorDialogCommand}" />
            </Grid>
        </Border>
    </Grid>
</Window>
