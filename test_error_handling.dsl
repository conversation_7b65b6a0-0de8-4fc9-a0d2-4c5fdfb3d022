# 测试错误处理的DSL文件 - 包含各种错误

metadata:
  name: "ErrorHandlingTest"
  description: "Test error handling"

environment:
  bus_type: "INVALID_BUS"  # 错误：无效的总线类型
  ecu_type: "APP"
  addressing: "Physical"
  baudrate: "not_a_number"  # 错误：波特率不是数字

test_steps:
  - step: 1
    description: "测试步骤1"
    action:
      type: "send_and_verify"
      # 错误：缺少必需的send字段
      expect: "04 62 F1 86 ** 00 00 00"
      timeout: "invalid_timeout"  # 错误：超时值无效

  - step: 2
    description: "测试步骤2"
    action:
      type: "invalid_action_type"  # 错误：无效的动作类型
      send: "03 22 F1 86 00 00 00 00"
      expect: "04 62 F1 86 ** 00 00 00"

  - step: "not_a_number"  # 错误：步骤编号不是数字
    description: "测试步骤3"
    action:
      type: "flow_control_sequence"
      # 错误：缺少必需的fc_frame字段
      expect_cf_pattern: "2* ** ** ** ** ** ** **"
      max_frames: "not_a_number"  # 错误：最大帧数不是数字
