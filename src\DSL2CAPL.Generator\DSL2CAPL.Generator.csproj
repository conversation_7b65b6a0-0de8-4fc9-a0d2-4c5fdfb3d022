<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <AssemblyTitle>DSL2CAPL Generator Library</AssemblyTitle>
    <AssemblyDescription>Code generation library for DSL2CAPL converter</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Scriban" Version="5.9.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DSL2CAPL.Core\DSL2CAPL.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Templates\" />
    <Folder Include="CAPL\" />
    <Folder Include="DSL\" />
  </ItemGroup>

</Project>
