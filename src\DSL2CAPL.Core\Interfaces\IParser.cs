using DSL2CAPL.Core.Models;

namespace DSL2CAPL.Core.Interfaces;

/// <summary>
/// DSL解析器接口
/// </summary>
public interface IDslParser
{
    /// <summary>
    /// 解析DSL文本为测试用例对象
    /// </summary>
    /// <param name="dslContent">DSL文本内容</param>
    /// <returns>解析结果</returns>
    Task<ParseResult<TestCase>> ParseAsync(string dslContent);

    /// <summary>
    /// 验证DSL语法
    /// </summary>
    /// <param name="dslContent">DSL文本内容</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateSyntaxAsync(string dslContent);

    /// <summary>
    /// 获取DSL语法提示信息
    /// </summary>
    /// <param name="dslContent">DSL文本内容</param>
    /// <param name="position">光标位置</param>
    /// <returns>语法提示列表</returns>
    Task<List<SyntaxSuggestion>> GetSyntaxSuggestionsAsync(string dslContent, int position);
}

/// <summary>
/// CAPL解析器接口
/// </summary>
public interface ICaplParser
{
    /// <summary>
    /// 解析CAPL代码为测试用例对象
    /// </summary>
    /// <param name="caplContent">CAPL代码内容</param>
    /// <returns>解析结果</returns>
    Task<ParseResult<TestCase>> ParseAsync(string caplContent);

    /// <summary>
    /// 验证CAPL代码语法
    /// </summary>
    /// <param name="caplContent">CAPL代码内容</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateSyntaxAsync(string caplContent);

    /// <summary>
    /// 提取CAPL代码中的测试函数
    /// </summary>
    /// <param name="caplContent">CAPL代码内容</param>
    /// <returns>测试函数列表</returns>
    Task<List<CaplFunction>> ExtractTestFunctionsAsync(string caplContent);
}

/// <summary>
/// 解析结果
/// </summary>
/// <typeparam name="T">解析结果类型</typeparam>
public class ParseResult<T>
{
    /// <summary>
    /// 是否解析成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 解析结果
    /// </summary>
    public T? Result { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<ParseError> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<ParseWarning> Warnings { get; set; } = new();

    /// <summary>
    /// 解析耗时（毫秒）
    /// </summary>
    public long ElapsedMs { get; set; }
}

/// <summary>
/// 解析错误
/// </summary>
public class ParseError
{
    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 行号
    /// </summary>
    public int Line { get; set; }

    /// <summary>
    /// 列号
    /// </summary>
    public int Column { get; set; }

    /// <summary>
    /// 错误代码
    /// </summary>
    public string ErrorCode { get; set; } = string.Empty;

    /// <summary>
    /// 错误严重程度
    /// </summary>
    public ErrorSeverity Severity { get; set; } = ErrorSeverity.Error;
}

/// <summary>
/// 解析警告
/// </summary>
public class ParseWarning
{
    /// <summary>
    /// 警告消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 行号
    /// </summary>
    public int Line { get; set; }

    /// <summary>
    /// 列号
    /// </summary>
    public int Column { get; set; }

    /// <summary>
    /// 警告代码
    /// </summary>
    public string WarningCode { get; set; } = string.Empty;
}

/// <summary>
/// 语法提示
/// </summary>
public class SyntaxSuggestion
{
    /// <summary>
    /// 提示文本
    /// </summary>
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// 显示文本
    /// </summary>
    public string DisplayText { get; set; } = string.Empty;

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 提示类型
    /// </summary>
    public SuggestionType Type { get; set; } = SuggestionType.Keyword;

    /// <summary>
    /// 插入位置
    /// </summary>
    public int InsertPosition { get; set; }

    /// <summary>
    /// 替换长度
    /// </summary>
    public int ReplaceLength { get; set; }
}

/// <summary>
/// CAPL函数信息
/// </summary>
public class CaplFunction
{
    /// <summary>
    /// 函数名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 函数类型
    /// </summary>
    public CaplFunctionType Type { get; set; } = CaplFunctionType.TestFunction;

    /// <summary>
    /// 参数列表
    /// </summary>
    public List<CaplParameter> Parameters { get; set; } = new();

    /// <summary>
    /// 函数体
    /// </summary>
    public string Body { get; set; } = string.Empty;

    /// <summary>
    /// 开始行号
    /// </summary>
    public int StartLine { get; set; }

    /// <summary>
    /// 结束行号
    /// </summary>
    public int EndLine { get; set; }

    /// <summary>
    /// 注释
    /// </summary>
    public string Comment { get; set; } = string.Empty;
}

/// <summary>
/// CAPL参数信息
/// </summary>
public class CaplParameter
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 参数类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 默认值
    /// </summary>
    public string? DefaultValue { get; set; }

    /// <summary>
    /// 是否为数组
    /// </summary>
    public bool IsArray { get; set; } = false;
}

/// <summary>
/// 错误严重程度枚举
/// </summary>
public enum ErrorSeverity
{
    Info,
    Warning,
    Error,
    Fatal
}

/// <summary>
/// 提示类型枚举
/// </summary>
public enum SuggestionType
{
    Keyword,
    Function,
    Variable,
    Constant,
    Property,
    Method,
    Class,
    Interface,
    Enum,
    Snippet
}

/// <summary>
/// CAPL函数类型枚举
/// </summary>
public enum CaplFunctionType
{
    TestFunction,
    TestCase,
    OnMessage,
    OnKey,
    OnTimer,
    UserFunction,
    SystemFunction
}
