<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.5.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Moq" Version="4.20.69" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\DSL2CAPL.Core\DSL2CAPL.Core.csproj" />
    <ProjectReference Include="..\..\src\DSL2CAPL.Parser\DSL2CAPL.Parser.csproj" />
    <ProjectReference Include="..\..\src\DSL2CAPL.Generator\DSL2CAPL.Generator.csproj" />
    <ProjectReference Include="..\..\src\DSL2CAPL.Data\DSL2CAPL.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Core\" />
    <Folder Include="Parser\" />
    <Folder Include="Generator\" />
    <Folder Include="Data\" />
    <Folder Include="Integration\" />
    <Folder Include="TestData\" />
  </ItemGroup>

</Project>
