# DSL2CAPL产品详细技术方案

## 1. 产品概述

### 1.1 产品目标
开发一个DSL转换成CAPL代码的产品，通过编写DSL快速生成可运行的CAPL代码，提升测试用例编写效率、可靠性和可读性。同时借助AI识别测试用例，自动转化为DSL，再转化为CAPL。

### 1.2 核心价值
- **效率提升**：减少80%的手工编码时间
- **质量保证**：标准化的CAPL代码，减少错误
- **可维护性**：DSL代码易读易改
- **AI辅助**：自动识别和转换测试用例

## 2. 技术架构

### 2.1 整体架构
```
测试用例文档 → AI识别引擎 → DSL编辑器 → DSL验证器
                                    ↓
CAPL代码 ← 代码生成引擎 ← 双向转换引擎 ← 数据库存储
```

### 2.2 核心模块
1. **AI识别模块**：文档解析 → DSL生成
2. **DSL核心**：语法定义、解析器、验证器
3. **双向转换引擎**：DSL ↔ CAPL 相互转换和预览
4. **代码生成模块**：DSL → CAPL转换
5. **质量保证模块**：准确性验证、测试覆盖
6. **用户界面**：DSL编辑、预览、调试
7. **数据存储模块**：模板、规则、历史记录管理

## 3. 技术栈

### 3.1 后端技术栈
- **语言**：C# 12 (.NET 8)
- **IDE**：Visual Studio 2022
- **DSL解析**：ANTLR4 for C# / 自定义解析器
- **代码生成**：T4模板 / Scriban模板引擎
- **AI集成**：Azure OpenAI / OpenAI API
- **数据存储**：SQLite (Entity Framework Core) + JSON配置
- **API框架**：ASP.NET Core Web API
- **桌面应用**：WPF / WinUI 3

### 3.2 前端技术栈
- **桌面应用**：WPF + MVVM模式
- **代码编辑器**：AvalonEdit / ICSharpCode.TextEditor
- **UI框架**：Material Design in XAML / ModernWpf
- **图表组件**：OxyPlot / LiveCharts

### 3.3 开发工具
- **版本控制**：Git
- **测试框架**：xUnit + Moq
- **CI/CD**：Azure DevOps / GitHub Actions
- **文档**：DocFX
- **代码质量**：SonarQube + StyleCop

## 4. DSL设计

### 4.1 DSL语法结构
基于现有CAPL代码分析，设计如下DSL结构：

```yaml
# 测试用例元信息
metadata:
  name: "TG01_TC01_1021940"
  description: "Block size handling physical addressing"
  test_group: "TG01"

# 测试环境配置
environment:
  bus_type: "CAN" # CAN/CANFD
  ecu_type: "APP" # APP/PBL/SBL
  addressing: "PHYSICAL" # PHYSICAL/FUNCTIONAL

# 测试数据定义
data_definitions:
  constants:
    DIAG_REQ_ID: "0x123"
    P2_TIME: 50
  frames:
    session_request: "03 22 F1 86 00 00 00 00"

# 测试步骤
test_steps:
  - step: 1
    action:
      type: "send_and_verify"
      send: "${frames.session_request}"
      expect: "04 62 F1 86 01 00 00 00"
```

### 4.2 DSL语义模型
```csharp
public class TestCase
{
    public TestMetadata Metadata { get; set; }
    public TestEnvironment Environment { get; set; }
    public DataDefinitions DataDefinitions { get; set; }
    public List<TestStep> TestSteps { get; set; }
}
```

## 5. 准确性保证方案

### 5.1 多层验证机制
```
DSL输入 → 语法验证 → 语义验证 → 模板验证 → 生成验证 → 执行验证
```

### 5.2 代码生成准确性保证

#### 5.2.1 模板化生成系统
```csharp
public class CAPLGenerator
{
    public string GenerateCAPL(TestCase testCase)
    {
        var template = SelectTemplate(testCase.Environment);
        _validator.ValidateTestCase(testCase);
        var capl = _templateEngine.Render(template, testCase);
        _validator.ValidateGeneratedCAPL(capl);
        return capl;
    }
}
```

#### 5.2.2 双向转换验证
```csharp
public class BidirectionalConverter
{
    public bool ValidateRoundTrip(DSL originalDsl)
    {
        var capl = DSLToCAPL(originalDsl);
        var reconstructedDsl = CAPLToDSL(capl);
        return _dslComparer.AreEquivalent(originalDsl, reconstructedDsl);
    }
}
```

### 5.3 质量监控体系
- 自动化测试覆盖
- 代码质量指标监控
- 错误率统计分析
- 用户反馈收集

## 6. 数据库设计

### 6.1 数据库用途
1. **DSL模板存储**：常用的DSL模式和模板
2. **代码生成规则**：DSL到CAPL的映射规则
3. **测试用例历史**：版本管理和历史记录
4. **用户配置**：个人偏好和项目设置
5. **质量数据**：生成代码质量指标
6. **AI训练数据**：改进AI识别准确性

### 6.2 核心数据模型
```csharp
public class DSLTemplate
{
    public int Id { get; set; }
    public string Name { get; set; }
    public string Category { get; set; }
    public string DSLContent { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class GenerationRule
{
    public int Id { get; set; }
    public string DSLPattern { get; set; }
    public string CAPLTemplate { get; set; }
    public int Priority { get; set; }
}
```

## 7. 实施计划

### 阶段1：基础架构（4周）
- Visual Studio 2022解决方案搭建
- DSL语法设计和解析器
- WPF界面框架
- 数据库设计

### 阶段2：核心功能（6周）
- CAPL代码生成引擎
- 双向转换功能
- DSL编辑器
- 基础验证机制

### 阶段3：质量保证（4周）
- 完整验证体系
- 自动化测试
- 性能优化
- 错误处理

### 阶段4：AI增强（4周）
- AI识别模块
- 智能建议功能
- 用户体验优化
- 文档系统

## 8. 风险控制

### 8.1 技术风险
- DSL语法复杂性控制
- CAPL代码生成准确性
- 性能优化挑战

### 8.2 缓解措施
- 渐进式DSL设计
- 充分的测试覆盖
- 持续的质量监控

## 9. 成功指标

### 9.1 功能指标
- DSL到CAPL转换准确率 > 95%
- 代码生成速度 < 1秒
- 支持的测试场景覆盖率 > 80%

### 9.2 用户体验指标
- 学习成本 < 2小时
- 编码效率提升 > 80%
- 用户满意度 > 4.5/5

---

**文档版本**: 1.0  
**创建日期**: 2024-12-19  
**最后更新**: 2024-12-19  
**负责人**: 开发团队
