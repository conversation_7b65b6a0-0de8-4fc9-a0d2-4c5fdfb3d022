using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using Timer = System.Threading.Timer;

namespace DSL2CAPL.UI.Controls;

/// <summary>
/// DSL编辑器用户控件
/// </summary>
public partial class DslEditor : System.Windows.Controls.UserControl
{
    private bool _isUpdatingText = false;
    private Timer? _syntaxHighlightTimer;
    private string _lastText = "";

    public DslEditor()
    {
        InitializeComponent();
        InitializeEditor();
    }

    /// <summary>
    /// DSL文本内容依赖属性
    /// </summary>
    public static readonly DependencyProperty TextProperty =
        DependencyProperty.Register(nameof(Text), typeof(string), typeof(DslEditor),
            new PropertyMetadata(string.Empty, OnTextChanged));

    public string Text
    {
        get => (string)GetValue(TextProperty);
        set => SetValue(TextProperty, value);
    }

    /// <summary>
    /// 文本变化事件
    /// </summary>
    public event EventHandler<TextChangedEventArgs>? TextChanged;

    /// <summary>
    /// 初始化编辑器
    /// </summary>
    private void InitializeEditor()
    {
        // 设置初始内容
        UpdateLineNumbers();
        
        // 设置语法高亮定时器
        _syntaxHighlightTimer = new Timer(ApplySyntaxHighlighting, null, Timeout.Infinite, Timeout.Infinite);
    }

    /// <summary>
    /// 文本属性变化处理
    /// </summary>
    private static void OnTextChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is DslEditor editor && !editor._isUpdatingText)
        {
            editor.UpdateEditorContent((string)e.NewValue);
        }
    }

    /// <summary>
    /// 更新编辑器内容
    /// </summary>
    private void UpdateEditorContent(string newText)
    {
        if (_isUpdatingText) return;

        _isUpdatingText = true;

        try
        {
            // 获取当前文本
            var textRange = new TextRange(MainEditor.Document.ContentStart, MainEditor.Document.ContentEnd);
            var currentText = textRange.Text;

            // 只有当文本真正不同时才更新
            if (currentText != newText)
            {
                // 简单地更新文本，不保存光标位置
                textRange.Text = newText ?? "";

                // 设置光标到文档末尾
                MainEditor.CaretPosition = MainEditor.Document.ContentEnd;

                UpdateLineNumbers();
                ScheduleSyntaxHighlighting();
            }
        }
        finally
        {
            _isUpdatingText = false;
        }
    }

    /// <summary>
    /// 主编辑器文本变化事件
    /// </summary>
    private void MainEditor_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (_isUpdatingText) return;

        try
        {
            // 获取当前文本
            var textRange = new TextRange(MainEditor.Document.ContentStart, MainEditor.Document.ContentEnd);
            var currentText = textRange.Text;

            // 更新依赖属性
            _isUpdatingText = true;
            SetValue(TextProperty, currentText);
            _isUpdatingText = false;

            // 更新行号
            UpdateLineNumbers();

            // 更新状态
            UpdateStatus();

            // 安排语法高亮
            ScheduleSyntaxHighlighting();

            // 触发事件
            TextChanged?.Invoke(this, e);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"文本更新错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 键盘按下事件
    /// </summary>
    private void MainEditor_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        // Ctrl+Space 触发智能补全
        if (e.Key == Key.Space && Keyboard.Modifiers == ModifierKeys.Control)
        {
            ShowIntelliSense();
            e.Handled = true;
        }
        // Tab键处理
        else if (e.Key == Key.Tab)
        {
            if (IntelliSensePopup.IsOpen)
            {
                AcceptSuggestion();
                e.Handled = true;
            }
            else
            {
                // 插入4个空格而不是Tab字符
                InsertText("    ");
                e.Handled = true;
            }
        }
        // Escape关闭智能补全
        else if (e.Key == Key.Escape)
        {
            IntelliSensePopup.IsOpen = false;
            e.Handled = true;
        }
        // Enter键处理
        else if (e.Key == Key.Enter)
        {
            if (IntelliSensePopup.IsOpen)
            {
                AcceptSuggestion();
                e.Handled = true;
            }
            else
            {
                // 自动缩进
                HandleEnterKey();
            }
        }
    }

    /// <summary>
    /// 滚动变化事件
    /// </summary>
    private void MainEditor_ScrollChanged(object sender, ScrollChangedEventArgs e)
    {
        // 同步行号滚动
        LineNumberScrollViewer.ScrollToVerticalOffset(e.VerticalOffset);
    }

    /// <summary>
    /// 选择变化事件
    /// </summary>
    private void MainEditor_SelectionChanged(object sender, RoutedEventArgs e)
    {
        UpdateStatus();
        
        // 如果有选择，关闭智能补全
        if (!MainEditor.Selection.IsEmpty)
        {
            IntelliSensePopup.IsOpen = false;
        }
    }

    /// <summary>
    /// 建议列表键盘事件
    /// </summary>
    private void SuggestionListBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == Key.Enter || e.Key == Key.Tab)
        {
            AcceptSuggestion();
            e.Handled = true;
        }
        else if (e.Key == Key.Escape)
        {
            IntelliSensePopup.IsOpen = false;
            MainEditor.Focus();
            e.Handled = true;
        }
    }

    /// <summary>
    /// 建议列表双击事件
    /// </summary>
    private void SuggestionListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        AcceptSuggestion();
    }

    /// <summary>
    /// 显示智能补全
    /// </summary>
    private void ShowIntelliSense()
    {
        var textRange = new TextRange(MainEditor.Document.ContentStart, MainEditor.CaretPosition);
        var text = textRange.Text;
        var caretPosition = text.Length;

        var suggestions = DslIntelliSenseProvider.GetCompletionSuggestions(text, caretPosition);
        
        if (suggestions.Any())
        {
            SuggestionListBox.ItemsSource = suggestions;
            SuggestionListBox.SelectedIndex = 0;
            
            // 设置弹出位置
            var caretRect = MainEditor.CaretPosition.GetCharacterRect(LogicalDirection.Forward);
            IntelliSensePopup.HorizontalOffset = caretRect.Left;
            IntelliSensePopup.VerticalOffset = caretRect.Bottom;
            
            IntelliSensePopup.IsOpen = true;
            SuggestionListBox.Focus();
        }
    }

    /// <summary>
    /// 接受建议
    /// </summary>
    private void AcceptSuggestion()
    {
        if (SuggestionListBox.SelectedItem is string suggestion)
        {
            InsertText(suggestion);
            IntelliSensePopup.IsOpen = false;
            MainEditor.Focus();
        }
    }

    /// <summary>
    /// 插入文本
    /// </summary>
    private void InsertText(string text)
    {
        MainEditor.CaretPosition.InsertTextInRun(text);
        MainEditor.CaretPosition = MainEditor.CaretPosition.GetPositionAtOffset(text.Length) ?? MainEditor.CaretPosition;
    }

    /// <summary>
    /// 处理Enter键
    /// </summary>
    private void HandleEnterKey()
    {
        // 获取当前行的缩进
        var currentLine = GetCurrentLine();
        var indent = GetIndentation(currentLine);
        
        // 插入换行和缩进
        InsertText("\n" + indent);
    }

    /// <summary>
    /// 获取当前行文本
    /// </summary>
    private string GetCurrentLine()
    {
        var lineStart = MainEditor.CaretPosition.GetLineStartPosition(0);
        var lineEnd = MainEditor.CaretPosition.GetLineStartPosition(1) ?? MainEditor.Document.ContentEnd;
        
        var lineRange = new TextRange(lineStart, lineEnd);
        return lineRange.Text.TrimEnd('\r', '\n');
    }

    /// <summary>
    /// 获取行的缩进
    /// </summary>
    private string GetIndentation(string line)
    {
        var indent = "";
        foreach (char c in line)
        {
            if (c == ' ' || c == '\t')
                indent += c;
            else
                break;
        }
        
        // 如果行以冒号结尾，增加缩进
        if (line.TrimEnd().EndsWith(":"))
        {
            indent += "  ";
        }
        
        return indent;
    }

    /// <summary>
    /// 更新行号
    /// </summary>
    private void UpdateLineNumbers()
    {
        LineNumberPanel.Children.Clear();
        
        var textRange = new TextRange(MainEditor.Document.ContentStart, MainEditor.Document.ContentEnd);
        var lines = textRange.Text.Split('\n');
        
        for (int i = 1; i <= lines.Length; i++)
        {
            var lineNumber = new TextBlock
            {
                Text = i.ToString(),
                Style = (Style)FindResource("LineNumberStyle")
            };
            LineNumberPanel.Children.Add(lineNumber);
        }
    }

    /// <summary>
    /// 更新状态信息
    /// </summary>
    private void UpdateStatus()
    {
        try
        {
            // 获取光标位置
            var caretPosition = MainEditor.CaretPosition;
            var lineStart = caretPosition.GetLineStartPosition(0);
            var lineNumber = MainEditor.Document.ContentStart.GetOffsetToPosition(lineStart);
            
            // 计算行号和列号
            var textRange = new TextRange(MainEditor.Document.ContentStart, caretPosition);
            var text = textRange.Text;
            var lines = text.Split('\n');
            var currentLine = lines.Length;
            var currentColumn = lines.LastOrDefault()?.Length + 1 ?? 1;
            
            PositionText.Text = $"行: {currentLine}, 列: {currentColumn}";
            
            // 选择信息
            var selectionLength = MainEditor.Selection.Text.Length;
            SelectionText.Text = $"选择: {selectionLength}";
            
            // 状态信息
            StatusText.Text = selectionLength > 0 ? "已选择文本" : "就绪";
        }
        catch
        {
            PositionText.Text = "行: 1, 列: 1";
            SelectionText.Text = "选择: 0";
            StatusText.Text = "就绪";
        }
    }

    /// <summary>
    /// 安排语法高亮
    /// </summary>
    private void ScheduleSyntaxHighlighting()
    {
        _syntaxHighlightTimer?.Change(500, Timeout.Infinite); // 500ms延迟
    }

    /// <summary>
    /// 应用语法高亮
    /// </summary>
    private void ApplySyntaxHighlighting(object? state)
    {
        Dispatcher.Invoke(() =>
        {
            try
            {
                var textRange = new TextRange(MainEditor.Document.ContentStart, MainEditor.Document.ContentEnd);
                var currentText = textRange.Text;
                
                if (currentText != _lastText)
                {
                    _lastText = currentText;
                    // 这里可以添加语法高亮逻辑
                    // DslSyntaxHighlighter.ApplySyntaxHighlighting(MainEditor, currentText);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响编辑器功能
                System.Diagnostics.Debug.WriteLine($"语法高亮错误: {ex.Message}");
            }
        });
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    public void Cleanup()
    {
        _syntaxHighlightTimer?.Dispose();
    }
}
