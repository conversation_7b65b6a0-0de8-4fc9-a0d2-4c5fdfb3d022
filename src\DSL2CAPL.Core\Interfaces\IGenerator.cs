using DSL2CAPL.Core.Models;

namespace DSL2CAPL.Core.Interfaces;

/// <summary>
/// CAPL代码生成器接口
/// </summary>
public interface ICaplGenerator
{
    /// <summary>
    /// 从测试用例生成CAPL代码
    /// </summary>
    /// <param name="testCase">测试用例</param>
    /// <param name="options">生成选项</param>
    /// <returns>生成结果</returns>
    Task<GenerationResult> GenerateAsync(TestCase testCase, CaplGenerationOptions? options = null);

    /// <summary>
    /// 从多个测试用例生成CAPL代码
    /// </summary>
    /// <param name="testCases">测试用例列表</param>
    /// <param name="options">生成选项</param>
    /// <returns>生成结果</returns>
    Task<GenerationResult> GenerateAsync(IEnumerable<TestCase> testCases, CaplGenerationOptions? options = null);

    /// <summary>
    /// 验证生成的CAPL代码
    /// </summary>
    /// <param name="caplCode">CAPL代码</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateGeneratedCodeAsync(string caplCode);

    /// <summary>
    /// 获取可用的代码模板
    /// </summary>
    /// <returns>模板列表</returns>
    Task<List<CaplTemplate>> GetAvailableTemplatesAsync();
}

/// <summary>
/// DSL代码生成器接口
/// </summary>
public interface IDslGenerator
{
    /// <summary>
    /// 从测试用例生成DSL代码
    /// </summary>
    /// <param name="testCase">测试用例</param>
    /// <param name="options">生成选项</param>
    /// <returns>生成结果</returns>
    Task<GenerationResult> GenerateAsync(TestCase testCase, DslGenerationOptions? options = null);

    /// <summary>
    /// 从CAPL代码生成DSL代码
    /// </summary>
    /// <param name="caplCode">CAPL代码</param>
    /// <param name="options">生成选项</param>
    /// <returns>生成结果</returns>
    Task<GenerationResult> GenerateFromCaplAsync(string caplCode, DslGenerationOptions? options = null);

    /// <summary>
    /// 格式化DSL代码
    /// </summary>
    /// <param name="dslCode">DSL代码</param>
    /// <returns>格式化后的代码</returns>
    Task<string> FormatAsync(string dslCode);
}

/// <summary>
/// 双向转换器接口
/// </summary>
public interface IBidirectionalConverter
{
    /// <summary>
    /// DSL转CAPL
    /// </summary>
    /// <param name="dslContent">DSL内容</param>
    /// <param name="options">转换选项</param>
    /// <returns>转换结果</returns>
    Task<ConversionResult> DslToCaplAsync(string dslContent, ConversionOptions? options = null);

    /// <summary>
    /// CAPL转DSL
    /// </summary>
    /// <param name="caplContent">CAPL内容</param>
    /// <param name="options">转换选项</param>
    /// <returns>转换结果</returns>
    Task<ConversionResult> CaplToDslAsync(string caplContent, ConversionOptions? options = null);

    /// <summary>
    /// 验证往返转换的一致性
    /// </summary>
    /// <param name="originalDsl">原始DSL</param>
    /// <returns>验证结果</returns>
    Task<RoundTripValidationResult> ValidateRoundTripAsync(string originalDsl);

    /// <summary>
    /// 预览转换结果
    /// </summary>
    /// <param name="sourceContent">源内容</param>
    /// <param name="sourceType">源类型</param>
    /// <returns>预览结果</returns>
    Task<PreviewResult> PreviewConversionAsync(string sourceContent, SourceType sourceType);
}

/// <summary>
/// 代码生成结果
/// </summary>
public class GenerationResult
{
    /// <summary>
    /// 是否生成成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 生成的代码
    /// </summary>
    public string GeneratedCode { get; set; } = string.Empty;

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<GenerationError> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<GenerationWarning> Warnings { get; set; } = new();

    /// <summary>
    /// 生成耗时（毫秒）
    /// </summary>
    public long ElapsedMs { get; set; }

    /// <summary>
    /// 使用的模板信息
    /// </summary>
    public string? TemplateUsed { get; set; }

    /// <summary>
    /// 生成的文件信息
    /// </summary>
    public List<GeneratedFile> GeneratedFiles { get; set; } = new();
}

/// <summary>
/// 转换结果
/// </summary>
public class ConversionResult
{
    /// <summary>
    /// 是否转换成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 转换后的内容
    /// </summary>
    public string ConvertedContent { get; set; } = string.Empty;

    /// <summary>
    /// 源类型
    /// </summary>
    public SourceType SourceType { get; set; }

    /// <summary>
    /// 目标类型
    /// </summary>
    public SourceType TargetType { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public List<ConversionError> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息
    /// </summary>
    public List<ConversionWarning> Warnings { get; set; } = new();

    /// <summary>
    /// 转换耗时（毫秒）
    /// </summary>
    public long ElapsedMs { get; set; }

    /// <summary>
    /// 转换质量评分（0-100）
    /// </summary>
    public int QualityScore { get; set; } = 100;
}

/// <summary>
/// 往返验证结果
/// </summary>
public class RoundTripValidationResult
{
    /// <summary>
    /// 是否验证通过
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 原始DSL
    /// </summary>
    public string OriginalDsl { get; set; } = string.Empty;

    /// <summary>
    /// 重构的DSL
    /// </summary>
    public string ReconstructedDsl { get; set; } = string.Empty;

    /// <summary>
    /// 中间CAPL代码
    /// </summary>
    public string IntermediateCapl { get; set; } = string.Empty;

    /// <summary>
    /// 差异信息
    /// </summary>
    public List<string> Differences { get; set; } = new();

    /// <summary>
    /// 相似度评分（0-100）
    /// </summary>
    public int SimilarityScore { get; set; } = 100;
}

/// <summary>
/// 预览结果
/// </summary>
public class PreviewResult
{
    /// <summary>
    /// 预览内容
    /// </summary>
    public string PreviewContent { get; set; } = string.Empty;

    /// <summary>
    /// 是否可以转换
    /// </summary>
    public bool CanConvert { get; set; }

    /// <summary>
    /// 预计转换质量
    /// </summary>
    public int EstimatedQuality { get; set; } = 100;

    /// <summary>
    /// 潜在问题
    /// </summary>
    public List<string> PotentialIssues { get; set; } = new();

    /// <summary>
    /// 建议
    /// </summary>
    public List<string> Suggestions { get; set; } = new();
}

/// <summary>
/// CAPL生成选项
/// </summary>
public class CaplGenerationOptions
{
    /// <summary>
    /// 目标CAPL版本
    /// </summary>
    public string TargetVersion { get; set; } = "12.0";

    /// <summary>
    /// 代码风格
    /// </summary>
    public CaplCodeStyle CodeStyle { get; set; } = CaplCodeStyle.Standard;

    /// <summary>
    /// 是否包含注释
    /// </summary>
    public bool IncludeComments { get; set; } = true;

    /// <summary>
    /// 是否优化代码
    /// </summary>
    public bool OptimizeCode { get; set; } = true;

    /// <summary>
    /// 模板名称
    /// </summary>
    public string? TemplateName { get; set; }

    /// <summary>
    /// 自定义参数
    /// </summary>
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}

/// <summary>
/// DSL生成选项
/// </summary>
public class DslGenerationOptions
{
    /// <summary>
    /// 格式化风格
    /// </summary>
    public DslFormatStyle FormatStyle { get; set; } = DslFormatStyle.Standard;

    /// <summary>
    /// 是否包含注释
    /// </summary>
    public bool IncludeComments { get; set; } = true;

    /// <summary>
    /// 缩进大小
    /// </summary>
    public int IndentSize { get; set; } = 2;

    /// <summary>
    /// 是否使用简化语法
    /// </summary>
    public bool UseSimplifiedSyntax { get; set; } = false;
}

/// <summary>
/// 转换选项
/// </summary>
public class ConversionOptions
{
    /// <summary>
    /// 是否保留原始注释
    /// </summary>
    public bool PreserveComments { get; set; } = true;

    /// <summary>
    /// 是否验证转换结果
    /// </summary>
    public bool ValidateResult { get; set; } = true;

    /// <summary>
    /// 转换质量阈值
    /// </summary>
    public int QualityThreshold { get; set; } = 80;

    /// <summary>
    /// 自定义映射规则
    /// </summary>
    public Dictionary<string, string> CustomMappings { get; set; } = new();
}

/// <summary>
/// 生成错误
/// </summary>
public class GenerationError
{
    public string Message { get; set; } = string.Empty;
    public string ErrorCode { get; set; } = string.Empty;
    public int Line { get; set; }
    public int Column { get; set; }
}

/// <summary>
/// 生成警告
/// </summary>
public class GenerationWarning
{
    public string Message { get; set; } = string.Empty;
    public string WarningCode { get; set; } = string.Empty;
    public int Line { get; set; }
    public int Column { get; set; }
}

/// <summary>
/// 转换错误
/// </summary>
public class ConversionError
{
    public string Message { get; set; } = string.Empty;
    public string ErrorCode { get; set; } = string.Empty;
    public SourceType SourceType { get; set; }
}

/// <summary>
/// 转换警告
/// </summary>
public class ConversionWarning
{
    public string Message { get; set; } = string.Empty;
    public string WarningCode { get; set; } = string.Empty;
    public SourceType SourceType { get; set; }
}

/// <summary>
/// 生成的文件信息
/// </summary>
public class GeneratedFile
{
    public string FileName { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
}

/// <summary>
/// CAPL模板信息
/// </summary>
public class CaplTemplate
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string TemplateContent { get; set; } = string.Empty;
    public List<string> SupportedFeatures { get; set; } = new();
}

/// <summary>
/// 源类型枚举
/// </summary>
public enum SourceType
{
    DSL,
    CAPL
}

/// <summary>
/// CAPL代码风格枚举
/// </summary>
public enum CaplCodeStyle
{
    Standard,
    Compact,
    Verbose,
    Custom
}

/// <summary>
/// DSL格式风格枚举
/// </summary>
public enum DslFormatStyle
{
    Standard,
    Compact,
    Verbose,
    Custom
}
