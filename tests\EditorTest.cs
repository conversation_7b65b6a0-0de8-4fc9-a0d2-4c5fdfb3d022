using System;
using System.IO;

namespace EditorTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== DSL编辑器修复验证 ===");
            
            // 测试DSL内容
            var testDsl = @"# Block Size Handling Test Case
metadata:
  name: ""TG01_TC01_BlockSizeHandling""
  description: ""Block size handling test""
  author: ""Test""
  version: ""1.0""

environment:
  bus_type: ""CAN""
  ecu_type: ""APP""
  addressing: ""PHYSICAL""
  baudrate: 500000

test_steps:
  - step: 1
    description: ""确认ECU Session状态""
    action:
      type: ""send_and_verify""
      send: ""03 22 F1 86 00 00 00 00""
      expect: ""04 62 F1 86 ** 00 00 00""
      timeout: 100";

            Console.WriteLine("测试DSL内容:");
            Console.WriteLine(testDsl);
            Console.WriteLine();
            
            // 保存到文件
            var testFile = @"d:\Software\agent_DSL2CAPL\examples\editor_test.dsl";
            File.WriteAllText(testFile, testDsl);
            
            Console.WriteLine($"DSL内容已保存到: {testFile}");
            Console.WriteLine("请在UI中打开此文件测试编辑器功能");
            Console.WriteLine();
            
            // 验证多行内容
            var lines = testDsl.Split('\n');
            Console.WriteLine($"DSL内容验证:");
            Console.WriteLine($"- 总行数: {lines.Length}");
            Console.WriteLine($"- 字符总数: {testDsl.Length}");
            Console.WriteLine($"- 包含metadata: {testDsl.Contains("metadata")}");
            Console.WriteLine($"- 包含test_steps: {testDsl.Contains("test_steps")}");
            
            Console.WriteLine();
            Console.WriteLine("✅ 如果UI编辑器能正常显示和编辑以上内容，说明修复成功！");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
