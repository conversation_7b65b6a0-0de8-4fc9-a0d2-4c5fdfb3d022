<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net7.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyTitle>DSL2CAPL Converter</AssemblyTitle>
    <AssemblyDescription>DSL to CAPL code converter application</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <!-- <ApplicationIcon>Resources\app.ico</ApplicationIcon> -->
  </PropertyGroup>

  <!-- 暂时移除外部依赖以验证项目结构 -->
  <!--
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="AvalonEdit" Version="********" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="7.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Microsoft.VisualBasic" Version="10.3.0" />
    <PackageReference Include="System.Windows.Forms" Version="4.0.0" />
  </ItemGroup>
  -->

  <ItemGroup>
    <ProjectReference Include="..\DSL2CAPL.Core\DSL2CAPL.Core.csproj" />
    <ProjectReference Include="..\DSL2CAPL.Parser\DSL2CAPL.Parser.csproj" />
    <ProjectReference Include="..\DSL2CAPL.Generator\DSL2CAPL.Generator.csproj" />
    <ProjectReference Include="..\DSL2CAPL.Data\DSL2CAPL.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Resources\**\*" />
  </ItemGroup>

</Project>
