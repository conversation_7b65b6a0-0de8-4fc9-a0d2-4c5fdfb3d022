/*
 * Generated CAPL code from DSL
 * Test Case: TG01_TC01_BlockSizeHandling
 * Description: Block size handling physical addressing - Default session
 * Author: DSL2CAPL Generator
 * Version: 1.0
 * Generated on: 2025-08-05 09:12:14
 * Generator: DSL2CAPL Converter v0.1.0
 */

includes
{
  // Include DSL2CAPL Common Library
  #include "../CommonLibrary/Common.can"
}

variables
{
  // Test case metadata
  char gTestCaseName[100] = "TG01_TC01_BlockSizeHandling";
  char gTestDescription[200] = "Block size handling physical addressing - Default session";

  // Test configuration
  const dword DIAG_REQ_ID = 0x123;
  const dword DIAG_RES_ID = 0x456;
  const dword BAUDRATE = 500000;

  // Test variables
  int gCurrentStep = 0;
  int gTestResult = 1; // 1 = PASS, 0 = FAIL
  char gTestLog[1000] = "";

  // Response handling variables
  int responseReceived = 0;
  message response;
  int consecutiveFrameCount = 0;

  // Timers
  timer responseTimer;
  timer waitTimer;
  timer cfTimer;
}

/*
 * Test case: TG01_TC01_BlockSizeHandling
 * Description: Block size handling physical addressing - Default session
 */
testcase TC_TG01_TC01_BlockSizeHandling()
{
  // Initialize common library
  CommonLibrary_Initialize();

  // Initialize test
  TestCaseStart("TG01_TC01_BlockSizeHandling", "Block size handling physical addressing - Default session");

  // Step 1: 确认ECU Session状态
  gCurrentStep = 1;
  if (!Step1___ECU_Session__())
  {
    TestCaseFail("Step 1 failed: 确认ECU Session状态");
    return;
  }

  // Step 2: 发送多帧数据请求
  gCurrentStep = 2;
  if (!Step2_________())
  {
    TestCaseFail("Step 2 failed: 发送多帧数据请求");
    return;
  }

  // Step 3: 发送流控制帧(BS=0)并接收连续帧
  gCurrentStep = 3;
  if (!Step3________BS_0_______())
  {
    TestCaseFail("Step 3 failed: 发送流控制帧(BS=0)并接收连续帧");
    return;
  }

  // Step 4: 重新发送多帧数据请求
  gCurrentStep = 4;
  if (!Step4___________())
  {
    TestCaseFail("Step 4 failed: 重新发送多帧数据请求");
    return;
  }

  // Step 5: 发送流控制帧(BS=1)并接收单个连续帧
  gCurrentStep = 5;
  if (!Step5________BS_1_________())
  {
    TestCaseFail("Step 5 failed: 发送流控制帧(BS=1)并接收单个连续帧");
    return;
  }

  // Step 6: 重复步骤5直到接收完整消息
  gCurrentStep = 6;
  if (!Step6_____5________())
  {
    TestCaseFail("Step 6 failed: 重复步骤5直到接收完整消息");
    return;
  }

  // Step 7: 测试不同BS值(BS=2到6)
  gCurrentStep = 7;
  if (!Step7_____BS__BS_2_6_())
  {
    TestCaseFail("Step 7 failed: 测试不同BS值(BS=2到6)");
    return;
  }

  // Step 8: 最终确认ECU Session状态
  gCurrentStep = 8;
  if (!Step8_____ECU_Session__())
  {
    TestCaseFail("Step 8 failed: 最终确认ECU Session状态");
    return;
  }

  // Test completed successfully
  TestCasePass("All test steps completed successfully");
}

/*
 * Step 1: 确认ECU Session状态
 */
int Step1___ECU_Session__()
{
  int result = 0;

  TestStepStart(1, "确认ECU Session状态");
  // To confirm ECU Session

  message diagReq msg;
  message diagRes response;
  int responseReceived = 0;

  // Prepare diagnostic request
  msg.id = DIAG_REQ_ID;
  msg.dlc = 8;
  // Send data: 03 22 F1 86 00 00 00 00
  msg.dlc = 8;
  msg.byte(0) = 0x03;
  msg.byte(1) = 0x22;
  msg.byte(2) = 0xF1;
  msg.byte(3) = 0x86;
  msg.byte(4) = 0x00;
  msg.byte(5) = 0x00;
  msg.byte(6) = 0x00;
  msg.byte(7) = 0x00;

  // Send request
  output(msg);

  // Wait for response (timeout: 100ms)
  setTimer(responseTimer, 100);
  responseReceived = 0;

  // Wait for diagnostic response
  while (!responseReceived && testGetTimer(responseTimer) > 0)
  {
    // Response would be received via message handler
    // on message DIAG_RES_ID { responseReceived = 1; response = this; }
    testWaitForTimeout(10);  // Small delay
  }

  // Verify response: 04 62 F1 86 ** 00 00 00
  if (responseReceived)
  {
    int responseValid = 1;
    if (response.byte(0) != 0x04)
    {
      write("Response byte 0 mismatch: expected 0x04, got 0x%02X", response.byte(0));
      responseValid = 0;
    }
    if (response.byte(1) != 0x62)
    {
      write("Response byte 1 mismatch: expected 0x62, got 0x%02X", response.byte(1));
      responseValid = 0;
    }
    if (response.byte(2) != 0xF1)
    {
      write("Response byte 2 mismatch: expected 0xF1, got 0x%02X", response.byte(2));
      responseValid = 0;
    }
    if (response.byte(3) != 0x86)
    {
      write("Response byte 3 mismatch: expected 0x86, got 0x%02X", response.byte(3));
      responseValid = 0;
    }
    if (response.byte(4) != 0x00)
    {
      write("Response byte 4 mismatch: expected 0x00, got 0x%02X", response.byte(4));
      responseValid = 0;
    }
    if (response.byte(5) != 0x00)
    {
      write("Response byte 5 mismatch: expected 0x00, got 0x%02X", response.byte(5));
      responseValid = 0;
    }
    if (response.byte(6) != 0x00)
    {
      write("Response byte 6 mismatch: expected 0x00, got 0x%02X", response.byte(6));
      responseValid = 0;
    }
    if (responseValid)
    {
      TestStepPass(1, "Response received and verified");
      result = 1;
    }
    else
    {
      TestStepFail(1, "Response verification failed");
      result = 0;
    }
  }
  else
  {
    TestStepFail(1, "No response received within timeout");
    result = 0;
  }

  return result;
}

/*
 * Step 2: 发送多帧数据请求
 */
int Step2_________()
{
  int result = 0;

  TestStepStart(2, "发送多帧数据请求");
  // Request multi-frame data

  message diagReq msg;
  message diagRes response;
  int responseReceived = 0;

  // Prepare diagnostic request
  msg.id = DIAG_REQ_ID;
  msg.dlc = 8;
  // Send data: 03 22 ED 20 00 00 00 00
  msg.dlc = 8;
  msg.byte(0) = 0x03;
  msg.byte(1) = 0x22;
  msg.byte(2) = 0xED;
  msg.byte(3) = 0x20;
  msg.byte(4) = 0x00;
  msg.byte(5) = 0x00;
  msg.byte(6) = 0x00;
  msg.byte(7) = 0x00;

  // Send request
  output(msg);

  // Wait for response (timeout: 150ms)
  setTimer(responseTimer, 150);
  responseReceived = 0;

  // Wait for diagnostic response
  while (!responseReceived && testGetTimer(responseTimer) > 0)
  {
    // Response would be received via message handler
    // on message DIAG_RES_ID { responseReceived = 1; response = this; }
    testWaitForTimeout(10);  // Small delay
  }

  // Verify response: 1* ** 62 ED 20 ** ** **
  if (responseReceived)
  {
    int responseValid = 1;
    if (response.byte(0) != 0x16)
    {
      write("Response byte 0 mismatch: expected 0x16, got 0x%02X", response.byte(0));
      responseValid = 0;
    }
    if (response.byte(1) != 0x2E)
    {
      write("Response byte 1 mismatch: expected 0x2E, got 0x%02X", response.byte(1));
      responseValid = 0;
    }
    if (response.byte(2) != 0xD2)
    {
      write("Response byte 2 mismatch: expected 0xD2, got 0x%02X", response.byte(2));
      responseValid = 0;
    }
    if (responseValid)
    {
      TestStepPass(2, "Response received and verified");
      result = 1;
    }
    else
    {
      TestStepFail(2, "Response verification failed");
      result = 0;
    }
  }
  else
  {
    TestStepFail(2, "No response received within timeout");
    result = 0;
  }

  return result;
}

/*
 * Step 3: 发送流控制帧(BS=0)并接收连续帧
 */
int Step3________BS_0_______()
{
  int result = 0;

  TestStepStart(3, "发送流控制帧(BS=0)并接收连续帧");
  // BS=0, ECU will send all CFs continuously

  int result = 0;

  // Use common flow control function
  result = DiagCommon_FlowControlSequence(0x30, 0x00, 0x00, 15, "2* ** ** ** ** ** ** **");

  if (result)
  {
    TestStepPass(3, "Flow control sequence completed");
  }
  else
  {
    TestStepFail(3, "Flow control sequence failed");
  }

  return result;
}

/*
 * Step 4: 重新发送多帧数据请求
 */
int Step4___________()
{
  int result = 0;

  TestStepStart(4, "重新发送多帧数据请求");

  message diagReq msg;
  message diagRes response;
  int responseReceived = 0;

  // Prepare diagnostic request
  msg.id = DIAG_REQ_ID;
  msg.dlc = 8;
  // Send data: 03 22 ED 20 00 00 00 00
  msg.dlc = 8;
  msg.byte(0) = 0x03;
  msg.byte(1) = 0x22;
  msg.byte(2) = 0xED;
  msg.byte(3) = 0x20;
  msg.byte(4) = 0x00;
  msg.byte(5) = 0x00;
  msg.byte(6) = 0x00;
  msg.byte(7) = 0x00;

  // Send request
  output(msg);

  // Wait for response (timeout: 150ms)
  setTimer(responseTimer, 150);
  responseReceived = 0;

  // Wait for diagnostic response
  while (!responseReceived && testGetTimer(responseTimer) > 0)
  {
    // Response would be received via message handler
    // on message DIAG_RES_ID { responseReceived = 1; response = this; }
    testWaitForTimeout(10);  // Small delay
  }

  // Verify response: 1* ** 62 ED 20 ** ** **
  if (responseReceived)
  {
    int responseValid = 1;
    if (response.byte(0) != 0x16)
    {
      write("Response byte 0 mismatch: expected 0x16, got 0x%02X", response.byte(0));
      responseValid = 0;
    }
    if (response.byte(1) != 0x2E)
    {
      write("Response byte 1 mismatch: expected 0x2E, got 0x%02X", response.byte(1));
      responseValid = 0;
    }
    if (response.byte(2) != 0xD2)
    {
      write("Response byte 2 mismatch: expected 0xD2, got 0x%02X", response.byte(2));
      responseValid = 0;
    }
    if (responseValid)
    {
      TestStepPass(4, "Response received and verified");
      result = 1;
    }
    else
    {
      TestStepFail(4, "Response verification failed");
      result = 0;
    }
  }
  else
  {
    TestStepFail(4, "No response received within timeout");
    result = 0;
  }

  return result;
}

/*
 * Step 5: 发送流控制帧(BS=1)并接收单个连续帧
 */
int Step5________BS_1_________()
{
  int result = 0;

  TestStepStart(5, "发送流控制帧(BS=1)并接收单个连续帧");
  // BS=1, only one CF shall be sent in the block

  int result = 0;

  // Use common flow control function
  result = DiagCommon_FlowControlSequence(0x30, 0x01, 0x00, 1, "2* ** ** ** ** ** ** **");

  if (result)
  {
    TestStepPass(5, "Flow control sequence completed");
  }
  else
  {
    TestStepFail(5, "Flow control sequence failed");
  }

  return result;
}

/*
 * Step 6: 重复步骤5直到接收完整消息
 */
int Step6_____5________()
{
  int result = 0;

  TestStepStart(6, "重复步骤5直到接收完整消息");

  int result = 0;
  int iteration = 0;
  int messageComplete = 0;
  int stepResult = 0;

  // Repeat flow control until message is complete
  while (!messageComplete && iteration < 20)
  {
    iteration++;
    write("Flow control iteration %d", iteration);

    // Execute flow control sequence
    stepResult = DiagCommon_FlowControlSequence(0x30, 0x01, 0x00, 1, "2* ** ** ** ** ** ** **");

    if (!stepResult)
    {
      TestStepFail(6, "Flow control sequence failed in iteration %d", iteration);
      return 0;
    }

    // Check if message is complete
    messageComplete = DiagCommon_IsMessageComplete();

    if (messageComplete)
    {
      write("Message completed after %d iterations", iteration);
      break;
    }

    // Small delay between iterations
    testWaitForTimeout(50);
  }

  if (messageComplete)
  {
    TestStepPass(6, "Repeat flow control completed successfully after %d iterations", iteration);
    result = 1;
  }
  else
  {
    TestStepFail(6, "Message not completed after %d iterations", iteration);
    result = 0;
  }

  return result;
}

/*
 * Step 7: 测试不同BS值(BS=2到6)
 */
int Step7_____BS__BS_2_6_()
{
  int result = 0;

  TestStepStart(7, "测试不同BS值(BS=2到6)");

  message fcFrame, reqFrame;
  int bs, startBs, endBs;

  startBs = 2;
  endBs = 6;
  for (bs = startBs; bs <= endBs; bs++)
  {
    write("Testing BS = %d", bs);
    // Send base request
    reqFrame.id = DIAG_REQ_ID;
    reqFrame.dlc = 8;
    reqFrame.byte(0) = 0x03;
    reqFrame.byte(1) = 0x22;
    reqFrame.byte(2) = 0xED;
    reqFrame.byte(3) = 0x20;
    reqFrame.byte(4) = 0x00;
    reqFrame.byte(5) = 0x00;
    reqFrame.byte(6) = 0x00;
    reqFrame.byte(7) = 0x00;
    output(reqFrame);
    testWaitForTimeout(50);
    // Send flow control frame with current BS
    fcFrame.id = DIAG_REQ_ID;
    fcFrame.dlc = 8;
    fcFrame.byte(0) = 0x30;
    fcFrame.byte(1) = bs;
    fcFrame.byte(2) = 0x00;
    for (int i = 3; i < 8; i++) fcFrame.byte(i) = 0x00;
    output(fcFrame);

    // Wait and verify consecutive frames
    setTimer(cfTimer, 1000);
    int cfReceived = 0;
    while (testGetTimer(cfTimer) > 0 && cfReceived < bs)
    {
      // Check for consecutive frames
      // cfReceived = CheckConsecutiveFrames(bs);
    }
    testWaitForTimeout(100);
  }
  TestStepPass(7, "Loop flow control completed");
  result = 1;

  return result;
}

/*
 * Step 8: 最终确认ECU Session状态
 */
int Step8_____ECU_Session__()
{
  int result = 0;

  TestStepStart(8, "最终确认ECU Session状态");
  // Final ECU Session confirmation

  message diagReq msg;
  message diagRes response;
  int responseReceived = 0;

  // Prepare diagnostic request
  msg.id = DIAG_REQ_ID;
  msg.dlc = 8;
  // Send data: 03 22 F1 86 00 00 00 00
  msg.dlc = 8;
  msg.byte(0) = 0x03;
  msg.byte(1) = 0x22;
  msg.byte(2) = 0xF1;
  msg.byte(3) = 0x86;
  msg.byte(4) = 0x00;
  msg.byte(5) = 0x00;
  msg.byte(6) = 0x00;
  msg.byte(7) = 0x00;

  // Send request
  output(msg);

  // Wait for response (timeout: 100ms)
  setTimer(responseTimer, 100);
  responseReceived = 0;

  // Wait for diagnostic response
  while (!responseReceived && testGetTimer(responseTimer) > 0)
  {
    // Response would be received via message handler
    // on message DIAG_RES_ID { responseReceived = 1; response = this; }
    testWaitForTimeout(10);  // Small delay
  }

  // Verify response: 04 62 F1 86 ** 00 00 00
  if (responseReceived)
  {
    int responseValid = 1;
    if (response.byte(0) != 0x04)
    {
      write("Response byte 0 mismatch: expected 0x04, got 0x%02X", response.byte(0));
      responseValid = 0;
    }
    if (response.byte(1) != 0x62)
    {
      write("Response byte 1 mismatch: expected 0x62, got 0x%02X", response.byte(1));
      responseValid = 0;
    }
    if (response.byte(2) != 0xF1)
    {
      write("Response byte 2 mismatch: expected 0xF1, got 0x%02X", response.byte(2));
      responseValid = 0;
    }
    if (response.byte(3) != 0x86)
    {
      write("Response byte 3 mismatch: expected 0x86, got 0x%02X", response.byte(3));
      responseValid = 0;
    }
    if (response.byte(4) != 0x00)
    {
      write("Response byte 4 mismatch: expected 0x00, got 0x%02X", response.byte(4));
      responseValid = 0;
    }
    if (response.byte(5) != 0x00)
    {
      write("Response byte 5 mismatch: expected 0x00, got 0x%02X", response.byte(5));
      responseValid = 0;
    }
    if (response.byte(6) != 0x00)
    {
      write("Response byte 6 mismatch: expected 0x00, got 0x%02X", response.byte(6));
      responseValid = 0;
    }
    if (responseValid)
    {
      TestStepPass(8, "Response received and verified");
      result = 1;
    }
    else
    {
      TestStepFail(8, "Response verification failed");
      result = 0;
    }
  }
  else
  {
    TestStepFail(8, "No response received within timeout");
    result = 0;
  }

  return result;
}

/*
 * Message handlers
 */

on message DIAG_RES_ID
{
  // Handle diagnostic response
  responseReceived = 1;
  response = this;
  write("Received diagnostic response: ID=0x%03X, DLC=%d", this.id, this.dlc);
}

on message *
{
  // Handle consecutive frames for multi-frame transfers
  if ((this.byte(0) & 0xF0) == 0x20)  // Consecutive frame
  {
    consecutiveFrameCount++;
    write("Received consecutive frame #%d", consecutiveFrameCount);
  }
}

/*
 * Helper functions
 */

// Test framework functions (would be implemented in Common.can)
void TestCaseStart(char* name, char* description) { }
void TestCasePass(char* message) { }
void TestCaseFail(char* message) { }
void TestStepStart(int stepNumber, char* description) { }
void TestStepPass(int stepNumber, char* message) { }
void TestStepFail(int stepNumber, char* message) { }
