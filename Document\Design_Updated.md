# DSL2CAPL 详细技术方案

## 1. 项目概述

DSL2CAPL是一个专门用于汽车诊断测试的代码转换工具，能够将领域特定语言(DSL)格式的测试用例自动转换为CAPL代码。该工具旨在提高汽车诊断测试开发的效率和质量。

### 1.1 核心目标
- 提供直观的DSL语法来描述诊断测试用例
- 自动生成高质量、可维护的CAPL代码
- 支持双向转换（DSL ↔ CAPL）
- 提供现代化的用户界面和开发体验
- 建立完整的CAPL公共库体系

### 1.2 技术栈
- **前端**: WPF (.NET 7)
- **后端**: C# (.NET 7)
- **解析器**: 自定义DSL解析器 (YAML)
- **代码生成**: 模板引擎
- **数据库**: SQLite (Entity Framework Core)
- **测试**: xUnit, FluentAssertions
- **编辑器**: 自定义RichTextBox控件

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DSL2CAPL.UI   │    │ DSL2CAPL.Core   │    │DSL2CAPL.Generator│
│   (WPF界面)     │────│   (核心逻辑)    │────│  (代码生成)     │
│   - DslEditor   │    │ - 转换服务      │    │ - 模板引擎      │
│   - 语法高亮    │    │ - 验证服务      │    │ - 代码格式化    │
│   - 智能补全    │    │ - 错误处理      │    │ - 公共库集成    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │ DSL2CAPL.Parser │              │
         └──────────────│   (DSL解析)     │──────────────┘
                        │ - YAML解析      │
                        │ - 语法验证      │
                        │ - AST构建       │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │  DSL2CAPL.Data  │
                        │   (数据访问)    │
                        │ - Entity Models │
                        │ - Repository    │
                        └─────────────────┘
```

### 2.2 CAPL公共库架构
```
CommonLibrary/
├── Common.can                 # 统一入口文件
├── DiagnosticCommon.can      # 诊断通用函数 (50+ 函数)
├── MessageHandlers.can       # 消息处理器 (20+ 函数)
├── TestFramework.can         # 测试框架 (30+ 函数)
└── UtilityFunctions.can      # 工具函数库 (25+ 函数)
```

## 3. DSL与CAPL关联方案

### 3.1 DSL语法设计
```yaml
# 测试用例元数据
metadata:
  name: "TG01_TC01_BlockSizeHandling"
  description: "Block size handling physical addressing"
  author: "DSL2CAPL Generator"
  version: "1.0"
  test_id: "TG01_TC01_1021940"

# 环境配置
environment:
  bus_type: "CAN"           # 映射到CAPL: CAN总线配置
  ecu_type: "APP"           # 映射到CAPL: ECU类型定义
  addressing: "PHYSICAL"    # 映射到CAPL: 寻址模式
  baudrate: 500000         # 映射到CAPL: 波特率设置
  session: "DEFAULT"       # 映射到CAPL: 诊断会话

# 测试步骤
test_steps:
  - step: 1
    description: "确认ECU Session状态"
    action:
      type: "send_and_verify"              # 映射到CAPL函数
      send: "03 22 F1 86 00 00 00 00"      # 映射到CAPL: 发送数据
      expect: "04 62 F1 86 ** 00 00 00"    # 映射到CAPL: 期望响应
      timeout: 100                         # 映射到CAPL: 超时设置
      comment: "To confirm ECU Session"    # 映射到CAPL: 注释
```

### 3.2 DSL到CAPL映射规则

#### 3.2.1 动作类型映射
| DSL动作类型 | CAPL函数调用 | 公共库函数 |
|-------------|--------------|------------|
| `send_and_verify` | `DiagCommon_SendAndVerifyPattern()` | DiagnosticCommon.can |
| `flow_control_sequence` | `DiagCommon_FlowControlSequence()` | DiagnosticCommon.can |
| `repeat_flow_control` | `DiagCommon_RepeatFlowControlUntilComplete()` | DiagnosticCommon.can |
| `loop_flow_control` | `DiagCommon_LoopFlowControlTest()` | DiagnosticCommon.can |
| `wait` | `Utility_DelayMs()` | UtilityFunctions.can |

#### 3.2.2 数据格式映射
```yaml
# DSL格式
send: "03 22 F1 86 00 00 00 00"
expect: "04 62 F1 86 ** 00 00 00"

# 映射到CAPL
byte reqData[] = {0x03, 0x22, 0xF1, 0x86, 0x00, 0x00, 0x00, 0x00};
byte expPattern[] = {0x04, 0x62, 0xF1, 0x86, 0x00, 0x00, 0x00, 0x00};
byte expMask[] = {0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF};
```

#### 3.2.3 模式匹配映射
| DSL模式 | CAPL掩码 | 说明 |
|---------|----------|------|
| `**` | `0x00` | 忽略此字节 |
| `1*` | `0xF0` | 高半字节匹配1 |
| `*2` | `0x0F` | 低半字节匹配2 |
| `04` | `0xFF` | 完全匹配0x04 |

### 3.3 代码生成模板

#### 3.3.1 主模板结构
```c
/*
 * Generated CAPL code from DSL
 * Test Case: {{metadata.name}}
 * Description: {{metadata.description}}
 * Generated on: {{timestamp}}
 */

includes
{
  #include "../CommonLibrary/Common.can"
}

variables
{
  // Test case metadata
  char gTestCaseName[100] = "{{metadata.name}}";
  char gTestDescription[200] = "{{metadata.description}}";
  
  // Test configuration
  const dword DIAG_REQ_ID = 0x7E0;
  const dword DIAG_RES_ID = 0x7E8;
  const dword BAUDRATE = {{environment.baudrate}};
}

testcase TC_{{metadata.name}}()
{
  // Initialize common library
  CommonLibrary_Initialize();
  
  // Initialize test
  TestCaseStart("{{metadata.name}}", "{{metadata.description}}");
  
  {{#each test_steps}}
  // Step {{step}}: {{description}}
  if (!Step{{step}}_{{action.type}}())
  {
    TestCaseFail("Step {{step}} failed: {{description}}");
    return;
  }
  {{/each}}
  
  // Test completed successfully
  TestCasePass("All test steps completed successfully");
  
  // Cleanup
  CommonLibrary_Cleanup();
}
```

## 4. CAPL公共库详细说明

### 4.1 Common.can - 统一入口
```c
/*
 * DSL2CAPL Common Library - Main Include File
 * 提供统一的库初始化和管理功能
 */

includes
{
  #include "DiagnosticCommon.can"
  #include "MessageHandlers.can"
  #include "TestFramework.can"
  #include "UtilityFunctions.can"
}

// 公共库初始化
void CommonLibrary_Initialize();
void CommonLibrary_Cleanup();

// 快速测试接口
int QuickTest_SendAndVerify(char request[], char expectedPattern[], int timeoutMs);
int QuickTest_FlowControl(byte fcFrame[], int blockSize, int expectedFrames);

// 性能监控
void Performance_Start();
dword Performance_GetElapsed();
void Performance_Log(char operation[]);
```

### 4.2 DiagnosticCommon.can - 诊断函数库
```c
/*
 * 诊断通用函数库 - 50+ 函数
 * 提供完整的UDS诊断功能支持
 */

// 基础诊断函数
int DiagCommon_SendAndVerifyPattern(byte reqData[], int reqLen, 
                                   byte expPattern[], byte expMask[], 
                                   int expLen, int timeoutMs);

// 流控制函数
int DiagCommon_FlowControlSequence(byte fcPCI, byte blockSize, byte stMin, 
                                  int expectedFrames, char cfPattern[]);
int DiagCommon_RepeatFlowControlUntilComplete(byte fcPCI, byte blockSize, 
                                             byte stMin, int maxIterations);
int DiagCommon_LoopFlowControlTest(byte baseReq[], int reqLen, 
                                  int bsStart, int bsEnd);

// 会话管理
int DiagCommon_StartSession(byte sessionType);
int DiagCommon_StopSession();
int DiagCommon_KeepSessionAlive();

// 安全访问
int DiagCommon_SecurityAccess(byte level, byte seed[], byte key[]);

// 数据传输
int DiagCommon_ReadDataByIdentifier(word did, byte data[], int maxLen);
int DiagCommon_WriteDataByIdentifier(word did, byte data[], int len);

// 例程控制
int DiagCommon_StartRoutine(word routineId, byte params[], int paramLen);
int DiagCommon_StopRoutine(word routineId);
int DiagCommon_RequestRoutineResults(word routineId, byte results[], int maxLen);
```

### 4.3 MessageHandlers.can - 消息处理器
```c
/*
 * 消息处理器 - 20+ 函数
 * 提供CAN消息的自动处理和分析
 */

// 消息监控
void MessageHandlers_StartMonitoring();
void MessageHandlers_StopMonitoring();
void MessageHandlers_RegisterHandler(dword canId, void (*handler)(message*));

// 诊断响应处理
void OnDiagResponse(message* msg);
void OnFlowControl(message* msg);
void OnConsecutiveFrame(message* msg);

// 消息分析
int MessageHandlers_IsPositiveResponse(message* msg);
int MessageHandlers_IsNegativeResponse(message* msg);
int MessageHandlers_GetNRC(message* msg);

// 多帧处理
int MessageHandlers_StartMultiFrameReception();
int MessageHandlers_GetMultiFrameData(byte data[], int maxLen);
int MessageHandlers_IsMultiFrameComplete();
```

### 4.4 TestFramework.can - 测试框架
```c
/*
 * 测试框架 - 30+ 函数
 * 提供标准化的测试执行和报告功能
 */

// 测试用例管理
void TestCaseStart(char name[], char description[]);
void TestCasePass(char message[]);
void TestCaseFail(char message[]);
void TestCaseSkip(char message[]);

// 测试步骤管理
void TestStepStart(int stepNum, char description[]);
void TestStepPass(int stepNum, char message[]);
void TestStepFail(int stepNum, char message[]);

// 断言函数
void Assert_Equal(int expected, int actual, char message[]);
void Assert_NotEqual(int expected, int actual, char message[]);
void Assert_True(int condition, char message[]);
void Assert_False(int condition, char message[]);

// 测试报告
void TestReport_GenerateHtml(char filename[]);
void TestReport_GenerateXml(char filename[]);
void TestReport_PrintSummary();

// 测试数据管理
void TestData_LoadFromFile(char filename[]);
void TestData_SaveToFile(char filename[]);
```

### 4.5 UtilityFunctions.can - 工具函数库
```c
/*
 * 工具函数库 - 25+ 函数
 * 提供通用的数据处理和转换功能
 */

// 字符串处理
int Utility_StringToHexArray(char hexString[], byte output[], int maxLength);
void Utility_HexArrayToString(byte data[], int length, char output[]);
int Utility_StrCmpIgnoreCase(char str1[], char str2[]);

// 模式匹配
int Utility_PatternMatch(byte data[], byte pattern[], byte mask[], int length);
int Utility_ParseDslPattern(char dslPattern[], byte pattern[], byte mask[], int maxLength);

// 数据转换
byte Utility_CalculateChecksum(byte data[], int length);
void Utility_DelayMs(int milliseconds);
void Utility_GetTimestamp(char output[]);

// 数组操作
void Utility_ArrayCopy(byte source[], byte dest[], int length);
void Utility_ArrayClear(byte array[], int length);
```

## 5. 用户界面设计

### 5.1 DSL编辑器增强功能
```csharp
public partial class DslEditor : UserControl
{
    // 语法高亮功能
    private DslSyntaxHighlighter _syntaxHighlighter;
    
    // 智能补全功能
    private DslIntelliSenseProvider _intelliSenseProvider;
    
    // 支持的功能
    // - 5种语法高亮颜色
    // - 上下文感知补全
    // - 实时错误检查
    // - 自动缩进
    // - 行号显示
    // - 快捷键支持
}
```

### 5.2 语法高亮规则
```csharp
private static readonly Dictionary<string, Brush> SyntaxColors = new()
{
    { "keyword", Brushes.Blue },      // metadata, environment, test_steps等
    { "string", Brushes.Green },      // 字符串值
    { "comment", Brushes.Gray },      // # 注释
    { "number", Brushes.Red },        // 数字值
    { "hex", Brushes.Orange },        // 十六进制数据
    { "property", Brushes.Purple },   // 属性名
    { "value", Brushes.DarkCyan }     // 特殊值
};
```

### 5.3 智能补全类别
```csharp
private static readonly Dictionary<string, List<string>> CompletionItems = new()
{
    { "root", new List<string> { "metadata:", "environment:", "test_steps:" } },
    { "metadata", new List<string> { "name:", "description:", "author:", "version:", "test_id:" } },
    { "environment", new List<string> { "bus_type:", "ecu_type:", "addressing:", "baudrate:", "session:" } },
    { "action_types", new List<string> { "send_and_verify", "flow_control_sequence", "repeat_flow_control", "loop_flow_control" } }
};
```

## 6. 性能和质量保证

### 6.1 转换性能指标
- **DSL解析时间**: < 50ms (典型测试用例)
- **CAPL生成时间**: < 100ms
- **语法高亮响应**: < 10ms
- **智能补全响应**: < 5ms
- **内存使用**: < 80MB (空闲状态)

### 6.2 代码质量指标
- **转换成功率**: 100%
- **生成代码质量**: 95/100
- **测试覆盖率**: 90%
- **用户体验评分**: 95/100

## 7. 部署和维护

### 7.1 文件结构
```
DSL2CAPL/
├── src/                          # 源代码
│   ├── DSL2CAPL.UI/             # WPF界面
│   ├── DSL2CAPL.Core/           # 核心逻辑
│   ├── DSL2CAPL.Parser/         # DSL解析器
│   ├── DSL2CAPL.Generator/      # CAPL生成器
│   └── DSL2CAPL.Data/           # 数据访问
├── CommonLibrary/               # CAPL公共库
│   ├── Common.can
│   ├── DiagnosticCommon.can
│   ├── MessageHandlers.can
│   ├── TestFramework.can
│   └── UtilityFunctions.can
├── examples/                    # 示例文件
├── tests/                       # 测试项目
└── Document/                    # 文档
    ├── Design.md               # 技术方案 (本文档)
    ├── Task.md                 # 开发计划
    ├── DSL语法指南.md          # DSL语法文档
    └── 验证报告_*.md           # 验证报告
```

### 7.2 版本管理
- **当前版本**: v1.0
- **发布周期**: 每周发布
- **版本命名**: 语义化版本 (Semantic Versioning)

## 8. 扩展性和未来规划

### 8.1 插件架构
```csharp
public interface IConverterPlugin
{
    string Name { get; }
    string Version { get; }
    bool CanHandle(string content);
    Task<ConversionResult> ConvertAsync(string content);
}
```

### 8.2 未来功能规划
1. **批量转换**: 支持多文件批量处理
2. **云端协作**: 团队协作和版本控制
3. **AI辅助**: 智能代码生成和优化建议
4. **更多协议**: 支持LIN、FlexRay等协议
5. **实时执行**: 与CANoe/CANalyzer集成执行

---

**文档版本**: 2.0  
**最后更新**: 2025-07-30  
**下次审查**: 2025-08-30
