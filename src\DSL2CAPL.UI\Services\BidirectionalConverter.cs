using DSL2CAPL.Core.Services;
using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;

namespace DSL2CAPL.UI.Services;

/// <summary>
/// 简化的双向转换器实现
/// </summary>
public class BidirectionalConverter
{
    private readonly SimpleDslParser _dslParser;
    private readonly SimpleCaplGenerator _caplGenerator;

    /// <summary>
    /// 构造函数
    /// </summary>
    public BidirectionalConverter(
        SimpleDslParser dslParser,
        SimpleCaplGenerator caplGenerator)
    {
        _dslParser = dslParser ?? throw new ArgumentNullException(nameof(dslParser));
        _caplGenerator = caplGenerator ?? throw new ArgumentNullException(nameof(caplGenerator));
    }

    /// <summary>
    /// DSL转换为CAPL
    /// </summary>
    /// <param name="dslContent">DSL内容</param>
    /// <returns>转换结果</returns>
    public async Task<ConversionResult> DslToCaplAsync(string dslContent)
    {
        var result = new ConversionResult();
        var startTime = DateTime.Now;

        try
        {
            // 检查输入内容是否为空
            if (string.IsNullOrWhiteSpace(dslContent))
            {
                result.Errors.Add("DSL内容为空，请输入有效的DSL内容");
                return result;
            }

            // 1. 解析DSL
            var testCase = await _dslParser.ParseAsync(dslContent);
            if (testCase == null)
            {
                result.Errors.Add("DSL解析失败：请检查DSL格式是否正确，确保包含metadata、environment和test_steps等必要部分");
                return result;
            }

            // 2. 生成CAPL代码
            var caplCode = await _caplGenerator.GenerateAsync(testCase);
            
            result.ConvertedContent = caplCode;
            result.IsSuccess = !caplCode.StartsWith("// 错误：");
            result.QualityScore = result.IsSuccess ? 95 : 0;
        }
        catch (Exception ex)
        {
            result.Errors.Add($"转换过程中发生错误: {ex.Message}");
        }
        finally
        {
            result.ElapsedMs = (long)(DateTime.Now - startTime).TotalMilliseconds;
        }

        return result;
    }
}
