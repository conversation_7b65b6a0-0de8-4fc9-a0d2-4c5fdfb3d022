using DSL2CAPL.Core.Services;
using DSL2CAPL.Core.Models;
using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;

namespace DSL2CAPL.UI.Services;

/// <summary>
/// 简化的双向转换器实现
/// </summary>
public class BidirectionalConverter
{
    private readonly SimpleDslParser _dslParser;
    private readonly SimpleCaplGenerator _caplGenerator;

    /// <summary>
    /// 构造函数
    /// </summary>
    public BidirectionalConverter(
        SimpleDslParser dslParser,
        SimpleCaplGenerator caplGenerator)
    {
        _dslParser = dslParser ?? throw new ArgumentNullException(nameof(dslParser));
        _caplGenerator = caplGenerator ?? throw new ArgumentNullException(nameof(caplGenerator));
    }

    /// <summary>
    /// DSL转换为CAPL
    /// </summary>
    /// <param name="dslContent">DSL内容</param>
    /// <returns>转换结果</returns>
    public async Task<ConversionResult> DslToCaplAsync(string dslContent)
    {
        var result = new ConversionResult();
        var startTime = DateTime.Now;

        try
        {
            // 检查输入内容是否为空
            if (string.IsNullOrWhiteSpace(dslContent))
            {
                result.Errors.Add("DSL内容为空，请输入有效的DSL内容");
                return result;
            }

            // 1. 解析DSL
            TestCase testCase;
            try
            {
                testCase = await _dslParser.ParseAsync(dslContent);
                if (testCase == null)
                {
                    result.Errors.Add("DSL解析失败：请检查DSL格式是否正确，确保包含metadata、environment和test_steps等必要部分");
                    return result;
                }
            }
            catch (InvalidOperationException ex)
            {
                // 捕获详细的解析错误
                result.Errors.Add(ex.Message);
                return result;
            }
            catch (ArgumentException ex)
            {
                // 捕获参数错误
                result.Errors.Add(ex.Message);
                return result;
            }

            // 2. 生成CAPL代码
            string caplCode;
            try
            {
                caplCode = await _caplGenerator.GenerateAsync(testCase);
            }
            catch (Exception ex)
            {
                result.Errors.Add($"CAPL代码生成失败: {ex.Message}");
                return result;
            }

            result.ConvertedContent = caplCode;
            result.IsSuccess = !caplCode.StartsWith("// 错误：");
            result.QualityScore = result.IsSuccess ? 95 : 0;

            // 如果生成的代码包含错误标记，提取错误信息
            if (caplCode.StartsWith("// 错误："))
            {
                result.Errors.Add(caplCode.Replace("// 错误：", "").Trim());
            }
        }
        catch (Exception ex)
        {
            // 捕获其他未预期的错误
            result.Errors.Add($"转换过程中发生未预期的错误: {ex.Message}");
            if (ex.InnerException != null)
            {
                result.Errors.Add($"详细错误: {ex.InnerException.Message}");
            }
        }
        finally
        {
            result.ElapsedMs = (long)(DateTime.Now - startTime).TotalMilliseconds;
        }

        return result;
    }
}
