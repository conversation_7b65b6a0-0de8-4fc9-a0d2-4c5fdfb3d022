/*
 * Test Framework for DSL2CAPL
 * 测试框架公共方法
 * 
 * Purpose: 提供标准化的测试执行和报告功能
 * Author: DSL2CAPL Generator
 * Version: 1.0
 * Created: 2025-07-30
 */

#ifndef TEST_FRAMEWORK_CAN
#define TEST_FRAMEWORK_CAN

/*
 * 测试框架变量
 */
variables
{
  // 测试状态
  char gCurrentTestCase[100] = "";
  char gCurrentTestDescription[200] = "";
  int gCurrentStepNumber = 0;
  int gTestCaseResult = 1; // 1=PASS, 0=FAIL
  int gTotalSteps = 0;
  int gPassedSteps = 0;
  int gFailedSteps = 0;
  
  // 测试时间
  float gTestCaseStartTime = 0;
  float gTestStepStartTime = 0;
  
  // 测试日志
  char gTestLog[4096] = "";
  int gTestLogLength = 0;
  
  // 测试配置
  int gVerboseLogging = 1;
  int gStopOnFirstFailure = 0;
}

/*
 * 测试用例开始
 * @param testCaseName: 测试用例名称
 * @param description: 测试用例描述
 */
void TestCaseStart(char testCaseName[], char description[])
{
  gTestCaseStartTime = timeNowFloat() / 100.0;
  
  strncpy(gCurrentTestCase, testCaseName, sizeof(gCurrentTestCase) - 1);
  strncpy(gCurrentTestDescription, description, sizeof(gCurrentTestDescription) - 1);
  
  gCurrentStepNumber = 0;
  gTestCaseResult = 1;
  gTotalSteps = 0;
  gPassedSteps = 0;
  gFailedSteps = 0;
  
  // 清空测试日志
  gTestLog[0] = '\0';
  gTestLogLength = 0;
  
  write("=== TEST CASE START ===");
  write("Name: %s", testCaseName);
  write("Description: %s", description);
  write("Start Time: %.3f", gTestCaseStartTime);
  write("========================");
  
  TestFramework_AddToLog("TEST CASE STARTED: %s", testCaseName);
}

/*
 * 测试用例结束 - 成功
 * @param message: 成功消息
 */
void TestCasePass(char message[])
{
  float endTime = timeNowFloat() / 100.0;
  float duration = endTime - gTestCaseStartTime;
  
  write("=== TEST CASE PASS ===");
  write("Message: %s", message);
  write("Duration: %.3f seconds", duration);
  write("Steps: %d total, %d passed, %d failed", gTotalSteps, gPassedSteps, gFailedSteps);
  write("======================");
  
  TestFramework_AddToLog("TEST CASE PASSED: %s (%.3fs)", message, duration);
  TestFramework_GenerateReport();
}

/*
 * 测试用例结束 - 失败
 * @param message: 失败消息
 */
void TestCaseFail(char message[])
{
  float endTime = timeNowFloat() / 100.0;
  float duration = endTime - gTestCaseStartTime;
  
  gTestCaseResult = 0;
  
  write("=== TEST CASE FAIL ===");
  write("Message: %s", message);
  write("Duration: %.3f seconds", duration);
  write("Steps: %d total, %d passed, %d failed", gTotalSteps, gPassedSteps, gFailedSteps);
  write("======================");
  
  TestFramework_AddToLog("TEST CASE FAILED: %s (%.3fs)", message, duration);
  TestFramework_GenerateReport();
}

/*
 * 测试步骤开始
 * @param stepNumber: 步骤编号
 * @param description: 步骤描述
 */
void TestStepStart(int stepNumber, char description[])
{
  gCurrentStepNumber = stepNumber;
  gTestStepStartTime = timeNowFloat() / 100.0;
  gTotalSteps++;
  
  if (gVerboseLogging)
  {
    write("--- Step %d: %s ---", stepNumber, description);
  }
  
  TestFramework_AddToLog("STEP %d START: %s", stepNumber, description);
}

/*
 * 测试步骤成功
 * @param stepNumber: 步骤编号
 * @param message: 成功消息
 */
void TestStepPass(int stepNumber, char message[])
{
  float endTime = timeNowFloat() / 100.0;
  float duration = endTime - gTestStepStartTime;
  
  gPassedSteps++;
  
  if (gVerboseLogging)
  {
    write("Step %d PASS: %s (%.3fs)", stepNumber, message, duration);
  }
  
  TestFramework_AddToLog("STEP %d PASS: %s (%.3fs)", stepNumber, message, duration);
}

/*
 * 测试步骤失败
 * @param stepNumber: 步骤编号
 * @param message: 失败消息
 */
void TestStepFail(int stepNumber, char message[])
{
  float endTime = timeNowFloat() / 100.0;
  float duration = endTime - gTestStepStartTime;
  
  gFailedSteps++;
  gTestCaseResult = 0;
  
  write("Step %d FAIL: %s (%.3fs)", stepNumber, message, duration);
  
  TestFramework_AddToLog("STEP %d FAIL: %s (%.3fs)", stepNumber, message, duration);
  
  if (gStopOnFirstFailure)
  {
    TestCaseFail("Stopped on first failure");
  }
}

/*
 * 测试步骤失败 - 带格式化参数
 * @param stepNumber: 步骤编号
 * @param format: 格式化字符串
 * @param ...: 可变参数
 */
void TestStepFailf(int stepNumber, char format[], ...)
{
  char message[256];
  // 注意: CAPL不支持可变参数，这里仅作为接口示例
  // 实际使用时需要预先格式化消息
  TestStepFail(stepNumber, format);
}

/*
 * 添加到测试日志
 * @param format: 格式化字符串
 * @param ...: 参数
 */
void TestFramework_AddToLog(char format[], ...)
{
  char timestamp[32];
  char logEntry[256];
  float currentTime = timeNowFloat() / 100.0;
  
  // 格式化时间戳
  snprintf(timestamp, sizeof(timestamp), "[%.3f]", currentTime);
  
  // 构造日志条目 (简化版本，实际需要处理可变参数)
  snprintf(logEntry, sizeof(logEntry), "%s %s\n", timestamp, format);
  
  // 添加到全局日志
  if (gTestLogLength + strlen(logEntry) < sizeof(gTestLog) - 1)
  {
    strcat(gTestLog, logEntry);
    gTestLogLength += strlen(logEntry);
  }
}

/*
 * 生成测试报告
 */
void TestFramework_GenerateReport()
{
  float endTime = timeNowFloat() / 100.0;
  float totalDuration = endTime - gTestCaseStartTime;
  
  write("");
  write("=============== TEST REPORT ===============");
  write("Test Case: %s", gCurrentTestCase);
  write("Description: %s", gCurrentTestDescription);
  write("Result: %s", gTestCaseResult ? "PASS" : "FAIL");
  write("Duration: %.3f seconds", totalDuration);
  write("Steps Summary:");
  write("  Total: %d", gTotalSteps);
  write("  Passed: %d", gPassedSteps);
  write("  Failed: %d", gFailedSteps);
  write("  Success Rate: %.1f%%", gTotalSteps > 0 ? (float)gPassedSteps / gTotalSteps * 100 : 0);
  write("==========================================");
  
  if (gVerboseLogging)
  {
    write("");
    write("Test Log:");
    write("%s", gTestLog);
  }
}

/*
 * 设置测试框架配置
 * @param verbose: 是否详细日志
 * @param stopOnFail: 是否在首次失败时停止
 */
void TestFramework_Configure(int verbose, int stopOnFail)
{
  gVerboseLogging = verbose;
  gStopOnFirstFailure = stopOnFail;
  
  write("Test Framework configured: verbose=%d, stopOnFail=%d", verbose, stopOnFail);
}

/*
 * 断言函数 - 相等
 * @param stepNumber: 步骤编号
 * @param actual: 实际值
 * @param expected: 期望值
 * @param message: 消息
 */
void TestFramework_AssertEqual(int stepNumber, int actual, int expected, char message[])
{
  if (actual == expected)
  {
    TestStepPass(stepNumber, message);
  }
  else
  {
    char failMsg[256];
    snprintf(failMsg, sizeof(failMsg), "%s - Expected: %d, Actual: %d", message, expected, actual);
    TestStepFail(stepNumber, failMsg);
  }
}

/*
 * 断言函数 - 不相等
 * @param stepNumber: 步骤编号
 * @param actual: 实际值
 * @param notExpected: 不期望的值
 * @param message: 消息
 */
void TestFramework_AssertNotEqual(int stepNumber, int actual, int notExpected, char message[])
{
  if (actual != notExpected)
  {
    TestStepPass(stepNumber, message);
  }
  else
  {
    char failMsg[256];
    snprintf(failMsg, sizeof(failMsg), "%s - Value should not be: %d", message, notExpected);
    TestStepFail(stepNumber, failMsg);
  }
}

/*
 * 断言函数 - 真值
 * @param stepNumber: 步骤编号
 * @param condition: 条件
 * @param message: 消息
 */
void TestFramework_AssertTrue(int stepNumber, int condition, char message[])
{
  if (condition)
  {
    TestStepPass(stepNumber, message);
  }
  else
  {
    char failMsg[256];
    snprintf(failMsg, sizeof(failMsg), "%s - Condition is false", message);
    TestStepFail(stepNumber, failMsg);
  }
}

/*
 * 断言函数 - 假值
 * @param stepNumber: 步骤编号
 * @param condition: 条件
 * @param message: 消息
 */
void TestFramework_AssertFalse(int stepNumber, int condition, char message[])
{
  if (!condition)
  {
    TestStepPass(stepNumber, message);
  }
  else
  {
    char failMsg[256];
    snprintf(failMsg, sizeof(failMsg), "%s - Condition is true", message);
    TestStepFail(stepNumber, failMsg);
  }
}

/*
 * 性能测试辅助函数
 * @param stepNumber: 步骤编号
 * @param operation: 操作描述
 * @param maxDurationMs: 最大允许时间(毫秒)
 * @param actualDurationMs: 实际耗时(毫秒)
 */
void TestFramework_AssertPerformance(int stepNumber, char operation[], int maxDurationMs, int actualDurationMs)
{
  if (actualDurationMs <= maxDurationMs)
  {
    char passMsg[256];
    snprintf(passMsg, sizeof(passMsg), "%s completed in %dms (limit: %dms)", operation, actualDurationMs, maxDurationMs);
    TestStepPass(stepNumber, passMsg);
  }
  else
  {
    char failMsg[256];
    snprintf(failMsg, sizeof(failMsg), "%s took %dms, exceeded limit of %dms", operation, actualDurationMs, maxDurationMs);
    TestStepFail(stepNumber, failMsg);
  }
}

#endif // TEST_FRAMEWORK_CAN
