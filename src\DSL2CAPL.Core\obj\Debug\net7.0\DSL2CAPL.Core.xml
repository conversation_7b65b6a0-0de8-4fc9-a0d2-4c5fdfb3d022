<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DSL2CAPL.Core</name>
    </assembly>
    <members>
        <member name="T:DSL2CAPL.Core.Interfaces.ICaplGenerator">
            <summary>
            CAPL代码生成器接口
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.ICaplGenerator.GenerateAsync(DSL2CAPL.Core.Models.TestCase,DSL2CAPL.Core.Interfaces.CaplGenerationOptions)">
            <summary>
            从测试用例生成CAPL代码
            </summary>
            <param name="testCase">测试用例</param>
            <param name="options">生成选项</param>
            <returns>生成结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.ICaplGenerator.GenerateAsync(System.Collections.Generic.IEnumerable{DSL2CAPL.Core.Models.TestCase},DSL2CAPL.Core.Interfaces.CaplGenerationOptions)">
            <summary>
            从多个测试用例生成CAPL代码
            </summary>
            <param name="testCases">测试用例列表</param>
            <param name="options">生成选项</param>
            <returns>生成结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.ICaplGenerator.ValidateGeneratedCodeAsync(System.String)">
            <summary>
            验证生成的CAPL代码
            </summary>
            <param name="caplCode">CAPL代码</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.ICaplGenerator.GetAvailableTemplatesAsync">
            <summary>
            获取可用的代码模板
            </summary>
            <returns>模板列表</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.IDslGenerator">
            <summary>
            DSL代码生成器接口
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IDslGenerator.GenerateAsync(DSL2CAPL.Core.Models.TestCase,DSL2CAPL.Core.Interfaces.DslGenerationOptions)">
            <summary>
            从测试用例生成DSL代码
            </summary>
            <param name="testCase">测试用例</param>
            <param name="options">生成选项</param>
            <returns>生成结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IDslGenerator.GenerateFromCaplAsync(System.String,DSL2CAPL.Core.Interfaces.DslGenerationOptions)">
            <summary>
            从CAPL代码生成DSL代码
            </summary>
            <param name="caplCode">CAPL代码</param>
            <param name="options">生成选项</param>
            <returns>生成结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IDslGenerator.FormatAsync(System.String)">
            <summary>
            格式化DSL代码
            </summary>
            <param name="dslCode">DSL代码</param>
            <returns>格式化后的代码</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.IBidirectionalConverter">
            <summary>
            双向转换器接口
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IBidirectionalConverter.DslToCaplAsync(System.String,DSL2CAPL.Core.Interfaces.ConversionOptions)">
            <summary>
            DSL转CAPL
            </summary>
            <param name="dslContent">DSL内容</param>
            <param name="options">转换选项</param>
            <returns>转换结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IBidirectionalConverter.CaplToDslAsync(System.String,DSL2CAPL.Core.Interfaces.ConversionOptions)">
            <summary>
            CAPL转DSL
            </summary>
            <param name="caplContent">CAPL内容</param>
            <param name="options">转换选项</param>
            <returns>转换结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IBidirectionalConverter.ValidateRoundTripAsync(System.String)">
            <summary>
            验证往返转换的一致性
            </summary>
            <param name="originalDsl">原始DSL</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IBidirectionalConverter.PreviewConversionAsync(System.String,DSL2CAPL.Core.Interfaces.SourceType)">
            <summary>
            预览转换结果
            </summary>
            <param name="sourceContent">源内容</param>
            <param name="sourceType">源类型</param>
            <returns>预览结果</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.GenerationResult">
            <summary>
            代码生成结果
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.GenerationResult.IsSuccess">
            <summary>
            是否生成成功
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.GenerationResult.GeneratedCode">
            <summary>
            生成的代码
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.GenerationResult.Errors">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.GenerationResult.Warnings">
            <summary>
            警告信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.GenerationResult.ElapsedMs">
            <summary>
            生成耗时（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.GenerationResult.TemplateUsed">
            <summary>
            使用的模板信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.GenerationResult.GeneratedFiles">
            <summary>
            生成的文件信息
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ConversionResult">
            <summary>
            转换结果
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionResult.IsSuccess">
            <summary>
            是否转换成功
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionResult.ConvertedContent">
            <summary>
            转换后的内容
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionResult.SourceType">
            <summary>
            源类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionResult.TargetType">
            <summary>
            目标类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionResult.Errors">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionResult.Warnings">
            <summary>
            警告信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionResult.ElapsedMs">
            <summary>
            转换耗时（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionResult.QualityScore">
            <summary>
            转换质量评分（0-100）
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.RoundTripValidationResult">
            <summary>
            往返验证结果
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.RoundTripValidationResult.IsValid">
            <summary>
            是否验证通过
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.RoundTripValidationResult.OriginalDsl">
            <summary>
            原始DSL
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.RoundTripValidationResult.ReconstructedDsl">
            <summary>
            重构的DSL
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.RoundTripValidationResult.IntermediateCapl">
            <summary>
            中间CAPL代码
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.RoundTripValidationResult.Differences">
            <summary>
            差异信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.RoundTripValidationResult.SimilarityScore">
            <summary>
            相似度评分（0-100）
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.PreviewResult">
            <summary>
            预览结果
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.PreviewResult.PreviewContent">
            <summary>
            预览内容
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.PreviewResult.CanConvert">
            <summary>
            是否可以转换
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.PreviewResult.EstimatedQuality">
            <summary>
            预计转换质量
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.PreviewResult.PotentialIssues">
            <summary>
            潜在问题
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.PreviewResult.Suggestions">
            <summary>
            建议
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.CaplGenerationOptions">
            <summary>
            CAPL生成选项
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplGenerationOptions.TargetVersion">
            <summary>
            目标CAPL版本
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplGenerationOptions.CodeStyle">
            <summary>
            代码风格
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplGenerationOptions.IncludeComments">
            <summary>
            是否包含注释
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplGenerationOptions.OptimizeCode">
            <summary>
            是否优化代码
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplGenerationOptions.TemplateName">
            <summary>
            模板名称
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplGenerationOptions.CustomParameters">
            <summary>
            自定义参数
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.DslGenerationOptions">
            <summary>
            DSL生成选项
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.DslGenerationOptions.FormatStyle">
            <summary>
            格式化风格
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.DslGenerationOptions.IncludeComments">
            <summary>
            是否包含注释
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.DslGenerationOptions.IndentSize">
            <summary>
            缩进大小
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.DslGenerationOptions.UseSimplifiedSyntax">
            <summary>
            是否使用简化语法
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ConversionOptions">
            <summary>
            转换选项
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionOptions.PreserveComments">
            <summary>
            是否保留原始注释
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionOptions.ValidateResult">
            <summary>
            是否验证转换结果
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionOptions.QualityThreshold">
            <summary>
            转换质量阈值
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ConversionOptions.CustomMappings">
            <summary>
            自定义映射规则
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.GenerationError">
            <summary>
            生成错误
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.GenerationWarning">
            <summary>
            生成警告
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ConversionError">
            <summary>
            转换错误
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ConversionWarning">
            <summary>
            转换警告
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.GeneratedFile">
            <summary>
            生成的文件信息
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.CaplTemplate">
            <summary>
            CAPL模板信息
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.SourceType">
            <summary>
            源类型枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.CaplCodeStyle">
            <summary>
            CAPL代码风格枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.DslFormatStyle">
            <summary>
            DSL格式风格枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.IDslParser">
            <summary>
            DSL解析器接口
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IDslParser.ParseAsync(System.String)">
            <summary>
            解析DSL文本为测试用例对象
            </summary>
            <param name="dslContent">DSL文本内容</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IDslParser.ValidateSyntaxAsync(System.String)">
            <summary>
            验证DSL语法
            </summary>
            <param name="dslContent">DSL文本内容</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.IDslParser.GetSyntaxSuggestionsAsync(System.String,System.Int32)">
            <summary>
            获取DSL语法提示信息
            </summary>
            <param name="dslContent">DSL文本内容</param>
            <param name="position">光标位置</param>
            <returns>语法提示列表</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ICaplParser">
            <summary>
            CAPL解析器接口
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.ICaplParser.ParseAsync(System.String)">
            <summary>
            解析CAPL代码为测试用例对象
            </summary>
            <param name="caplContent">CAPL代码内容</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.ICaplParser.ValidateSyntaxAsync(System.String)">
            <summary>
            验证CAPL代码语法
            </summary>
            <param name="caplContent">CAPL代码内容</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:DSL2CAPL.Core.Interfaces.ICaplParser.ExtractTestFunctionsAsync(System.String)">
            <summary>
            提取CAPL代码中的测试函数
            </summary>
            <param name="caplContent">CAPL代码内容</param>
            <returns>测试函数列表</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ParseResult`1">
            <summary>
            解析结果
            </summary>
            <typeparam name="T">解析结果类型</typeparam>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseResult`1.IsSuccess">
            <summary>
            是否解析成功
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseResult`1.Result">
            <summary>
            解析结果
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseResult`1.Errors">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseResult`1.Warnings">
            <summary>
            警告信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseResult`1.ElapsedMs">
            <summary>
            解析耗时（毫秒）
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ParseError">
            <summary>
            解析错误
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseError.Message">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseError.Line">
            <summary>
            行号
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseError.Column">
            <summary>
            列号
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseError.ErrorCode">
            <summary>
            错误代码
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseError.Severity">
            <summary>
            错误严重程度
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ParseWarning">
            <summary>
            解析警告
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseWarning.Message">
            <summary>
            警告消息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseWarning.Line">
            <summary>
            行号
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseWarning.Column">
            <summary>
            列号
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.ParseWarning.WarningCode">
            <summary>
            警告代码
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.SyntaxSuggestion">
            <summary>
            语法提示
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.SyntaxSuggestion.Text">
            <summary>
            提示文本
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.SyntaxSuggestion.DisplayText">
            <summary>
            显示文本
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.SyntaxSuggestion.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.SyntaxSuggestion.Type">
            <summary>
            提示类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.SyntaxSuggestion.InsertPosition">
            <summary>
            插入位置
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.SyntaxSuggestion.ReplaceLength">
            <summary>
            替换长度
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.CaplFunction">
            <summary>
            CAPL函数信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplFunction.Name">
            <summary>
            函数名称
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplFunction.Type">
            <summary>
            函数类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplFunction.Parameters">
            <summary>
            参数列表
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplFunction.Body">
            <summary>
            函数体
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplFunction.StartLine">
            <summary>
            开始行号
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplFunction.EndLine">
            <summary>
            结束行号
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplFunction.Comment">
            <summary>
            注释
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.CaplParameter">
            <summary>
            CAPL参数信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplParameter.Name">
            <summary>
            参数名称
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplParameter.Type">
            <summary>
            参数类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplParameter.DefaultValue">
            <summary>
            默认值
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Interfaces.CaplParameter.IsArray">
            <summary>
            是否为数组
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.ErrorSeverity">
            <summary>
            错误严重程度枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.SuggestionType">
            <summary>
            提示类型枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Interfaces.CaplFunctionType">
            <summary>
            CAPL函数类型枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.DataDefinitions">
            <summary>
            测试数据定义
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.DataDefinitions.Constants">
            <summary>
            常量定义
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.DataDefinitions.Frames">
            <summary>
            帧定义
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.DataDefinitions.Variables">
            <summary>
            变量定义
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.DataDefinitions.MessageIds">
            <summary>
            消息ID定义
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.DataDefinitions.TimingParameters">
            <summary>
            时序参数定义
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Models.DataDefinitions.Validate">
            <summary>
            验证数据定义的有效性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Models.VariableDefinition">
            <summary>
            变量定义
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.VariableDefinition.Type">
            <summary>
            变量类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.VariableDefinition.DefaultValue">
            <summary>
            默认值
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.VariableDefinition.Description">
            <summary>
            描述
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.VariableDefinition.IsArray">
            <summary>
            是否为数组
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.VariableDefinition.ArraySize">
            <summary>
            数组大小（如果是数组）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.VariableDefinition.MinValue">
            <summary>
            最小值（用于数值类型）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.VariableDefinition.MaxValue">
            <summary>
            最大值（用于数值类型）
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Models.VariableDefinition.Validate">
            <summary>
            验证变量定义的有效性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Models.VariableType">
            <summary>
            变量类型枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.TimingParameters">
            <summary>
            时序参数定义
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TimingParameters.P2TimeMs">
            <summary>
            P2时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TimingParameters.P2ExtTimeMs">
            <summary>
            P2扩展时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TimingParameters.NCrTimeoutMs">
            <summary>
            N_Cr超时时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TimingParameters.NBsTimeoutMs">
            <summary>
            N_Bs超时时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TimingParameters.NCsTimeMs">
            <summary>
            N_Cs时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TimingParameters.NAsTimeMs">
            <summary>
            N_As时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TimingParameters.STminMs">
            <summary>
            STmin最小时间（毫秒）
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Models.TimingParameters.Validate">
            <summary>
            验证时序参数的有效性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Models.TestCase">
            <summary>
            表示一个完整的测试用例
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestCase.Metadata">
            <summary>
            测试用例元信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestCase.Environment">
            <summary>
            测试环境配置
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestCase.DataDefinitions">
            <summary>
            测试数据定义
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestCase.TestSteps">
            <summary>
            测试步骤列表
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Models.TestCase.Validate">
            <summary>
            验证测试用例的有效性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Models.TestMetadata">
            <summary>
            测试用例元信息
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestMetadata.Name">
            <summary>
            测试用例名称
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestMetadata.Description">
            <summary>
            测试用例描述
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestMetadata.TestGroup">
            <summary>
            测试组
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestMetadata.Author">
            <summary>
            作者
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestMetadata.Version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestMetadata.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestMetadata.LastModified">
            <summary>
            最后修改时间
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.TestEnvironment">
            <summary>
            测试环境配置
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestEnvironment.BusType">
            <summary>
            总线类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestEnvironment.EcuType">
            <summary>
            ECU类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestEnvironment.AddressingType">
            <summary>
            寻址方式
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestEnvironment.SessionType">
            <summary>
            会话类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestEnvironment.BaudRate">
            <summary>
            波特率
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.BusType">
            <summary>
            总线类型枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.EcuType">
            <summary>
            ECU类型枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.AddressingType">
            <summary>
            寻址方式枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.SessionType">
            <summary>
            会话类型枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.ValidationResult">
            <summary>
            验证结果
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.ValidationResult.IsValid">
            <summary>
            是否有效
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.ValidationResult.Errors">
            <summary>
            错误信息列表
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.ValidationResult.Warnings">
            <summary>
            警告信息列表
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.TestStep">
            <summary>
            测试步骤
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestStep.StepNumber">
            <summary>
            步骤编号
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestStep.Description">
            <summary>
            步骤描述
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestStep.Action">
            <summary>
            测试动作
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestStep.Validation">
            <summary>
            验证规则
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestStep.Conditions">
            <summary>
            执行条件
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestStep.IsCritical">
            <summary>
            是否为关键步骤
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestStep.ExpectedDurationMs">
            <summary>
            预期执行时间（毫秒）
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.TestAction">
            <summary>
            测试动作基类
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestAction.ActionType">
            <summary>
            动作类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestAction.TimeoutMs">
            <summary>
            超时时间（毫秒）
            </summary>
        </member>
        <member name="M:DSL2CAPL.Core.Models.TestAction.ValidateParameters">
            <summary>
            验证动作参数的有效性
            </summary>
            <returns>验证结果</returns>
        </member>
        <member name="T:DSL2CAPL.Core.Models.SendAndVerifyAction">
            <summary>
            发送并验证动作
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.SendAndVerifyAction.SendFrame">
            <summary>
            发送的帧数据
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.SendAndVerifyAction.ExpectFrame">
            <summary>
            期望的响应帧
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.SendAndVerifyAction.IsFunctional">
            <summary>
            是否使用功能寻址
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.SendAndVerifyAction.SendData">
            <summary>
            发送数据（别名，兼容性）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.SendAndVerifyAction.ExpectedResponse">
            <summary>
            期望响应（别名，兼容性）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.SendAndVerifyAction.TimeoutMs">
            <summary>
            超时时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.SendAndVerifyAction.Comment">
            <summary>
            注释
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.FlowControlSequenceAction">
            <summary>
            流控制序列动作
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.FlowControlSequenceAction.FcFrame">
            <summary>
            流控制帧
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.FlowControlSequenceAction.ExpectCfCount">
            <summary>
            期望的连续帧数量
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.FlowControlSequenceAction.BlockSizes">
            <summary>
            块大小列表
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.FlowControlSequenceAction.CheckTiming">
            <summary>
            是否检查时序
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.FlowControlSequenceAction.FlowControlFrame">
            <summary>
            流控制帧（别名，兼容性）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.FlowControlSequenceAction.ExpectedConsecutiveFramePattern">
            <summary>
            期望的连续帧模式（别名，兼容性）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.FlowControlSequenceAction.MaxFrames">
            <summary>
            最大帧数
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.FlowControlSequenceAction.Comment">
            <summary>
            注释
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.WaitAction">
            <summary>
            等待动作
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.WaitAction.WaitTimeMs">
            <summary>
            等待时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.WaitAction.DurationMs">
            <summary>
            持续时间（别名，兼容性）
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.TestValidation">
            <summary>
            测试验证规则
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestValidation.Type">
            <summary>
            验证类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestValidation.Parameters">
            <summary>
            验证参数
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestValidation.EnableTimingCheck">
            <summary>
            是否启用时序检查
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestValidation.MinResponseTimeMs">
            <summary>
            最小响应时间（毫秒）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestValidation.MaxResponseTimeMs">
            <summary>
            最大响应时间（毫秒）
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.ValidationType">
            <summary>
            验证类型枚举
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.TestConditions">
            <summary>
            测试条件
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestConditions.Preconditions">
            <summary>
            前置条件
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestConditions.ExecutionCondition">
            <summary>
            执行条件
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestConditions.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.TestConditions.RetryIntervalMs">
            <summary>
            重试间隔（毫秒）
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.RepeatFlowControlAction">
            <summary>
            重复流控制动作
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.RepeatFlowControlAction.ActionType">
            <summary>
            动作类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.RepeatFlowControlAction.FcFrame">
            <summary>
            流控制帧
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.RepeatFlowControlAction.RepeatUntil">
            <summary>
            重复条件
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.RepeatFlowControlAction.MaxIterations">
            <summary>
            最大迭代次数
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Models.LoopFlowControlAction">
            <summary>
            循环流控制动作
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.LoopFlowControlAction.ActionType">
            <summary>
            动作类型
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.LoopFlowControlAction.BsRange">
            <summary>
            BS范围，格式为 "start-end"，如 "2-6"
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.LoopFlowControlAction.FcFrameTemplate">
            <summary>
            流控制帧模板
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Models.LoopFlowControlAction.BaseRequest">
            <summary>
            基础请求
            </summary>
        </member>
        <member name="T:DSL2CAPL.Core.Services.ConversionResult">
            <summary>
            简化的转换结果
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Services.ConversionResult.IsSuccess">
            <summary>
            转换是否成功
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Services.ConversionResult.ConvertedContent">
            <summary>
            转换后的内容
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Services.ConversionResult.Errors">
            <summary>
            错误列表
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Services.ConversionResult.QualityScore">
            <summary>
            质量评分（0-100）
            </summary>
        </member>
        <member name="P:DSL2CAPL.Core.Services.ConversionResult.ElapsedMs">
            <summary>
            转换耗时（毫秒）
            </summary>
        </member>
    </members>
</doc>
