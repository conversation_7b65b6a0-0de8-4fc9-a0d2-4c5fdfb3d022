namespace DSL2CAPL.UI.Infrastructure;

/// <summary>
/// 简化的日志接口
/// </summary>
public interface ILogger<T>
{
    void LogInformation(string message, params object[] args);
    void LogWarning(string message, params object[] args);
    void LogError(Exception exception, string message, params object[] args);
    void LogError(string message, params object[] args);
    void LogDebug(string message, params object[] args);
}

/// <summary>
/// 简化的日志实现
/// </summary>
public class ConsoleLogger<T> : ILogger<T>
{
    private readonly string _categoryName = typeof(T).Name;

    public void LogInformation(string message, params object[] args)
    {
        Console.WriteLine($"[INFO] {_categoryName}: {string.Format(message, args)}");
    }

    public void LogWarning(string message, params object[] args)
    {
        Console.WriteLine($"[WARN] {_categoryName}: {string.Format(message, args)}");
    }

    public void LogError(Exception exception, string message, params object[] args)
    {
        Console.WriteLine($"[ERROR] {_categoryName}: {string.Format(message, args)} - {exception.Message}");
    }

    public void LogError(string message, params object[] args)
    {
        Console.WriteLine($"[ERROR] {_categoryName}: {string.Format(message, args)}");
    }

    public void LogDebug(string message, params object[] args)
    {
        Console.WriteLine($"[DEBUG] {_categoryName}: {string.Format(message, args)}");
    }
}
