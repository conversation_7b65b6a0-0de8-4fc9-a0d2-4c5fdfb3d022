# DSL2CAPL 示例文件
# 基于您提供的CAPL代码设计的DSL语法示例

# 测试用例元信息
metadata:
  name: "TG01_TC01_1021940"
  description: "Block size handling physical addressing - Default session"
  author: "Test Engineer"
  version: "1.0"
  test_group: "TG01"
  test_case: "TC01"
  requirement_id: "1021940"

# 测试环境配置
environment:
  bus_type: "CAN"           # CAN/CANFD
  ecu_type: "APP"           # APP/PBL/SBL
  addressing: "PHYSICAL"    # PHYSICAL/FUNCTIONAL
  session: "DEFAULT"        # DEFAULT/PROGRAMMING/EXTENDED
  baudrate: 500000

# 测试数据定义
data_definitions:
  constants:
    DIAG_REQ_ID: "0x123"
    DIAG_RES_ID: "0x456"
    P2_TIME: 50
    P2_EXT_TIME: 5000
    N_CR: 1000
    N_BS: 1000
    
  frames:
    session_request: "03 22 F1 86 00 00 00 00"
    session_response_01: "04 62 F1 86 01 00 00 00"
    session_response_02: "04 62 F1 86 02 00 00 00"
    
    multi_frame_request: "03 22 ED 20 00 00 00 00"
    first_frame_response: "1* ** 62 ED 20 ** ** **"
    
    flow_control_frame: "30 00 00 00 00 00 00 00"
    consecutive_frame_pattern: "2* ** ** ** ** ** ** **"
    
  variables:
    block_size: 
      type: "integer"
      default: 0
      range: [0, 255]
    frame_count:
      type: "integer"
      default: 0
    expected_cf_count:
      type: "integer"
      default: 0

# 测试步骤
test_steps:
  - step: 1
    description: "To confirm ECU Session"
    action:
      type: "send_and_verify"
      send: "${frames.session_request}"
      expect: "${frames.session_response_01}"
      timeout: "${constants.P2_TIME}"
    validation:
      type: "exact_match"
      comment: "确认ECU会话状态"
      
  - step: 2
    description: "Request multi-frame response"
    action:
      type: "send_and_verify"
      send: "${frames.multi_frame_request}"
      expect: "${frames.first_frame_response}"
      timeout: "${constants.P2_TIME}"
    validation:
      type: "pattern_match"
      comment: "请求多帧响应，验证首帧格式"
      
  - step: 3
    description: "Send FC and receive CF frames with different block sizes"
    action:
      type: "flow_control_sequence"
      fc_frame: "${frames.flow_control_frame}"
      block_sizes: [0, 1, 2, 3, 4, 5, 6]
      expect_cf_pattern: "${frames.consecutive_frame_pattern}"
    validation:
      type: "frame_count_match"
      timing_check: true
      comment: "验证不同块大小下的连续帧数量"
      rules:
        - if: "block_size == 0"
          then: "expect_all_cf_frames_continuously"
        - if: "block_size > 0"
          then: "expect_cf_frames_equal_to_block_size"
          
  - step: 4
    description: "Verify timing requirements"
    action:
      type: "timing_verification"
      parameters:
        - check: "cf_frame_interval"
          min_time: 0
          max_time: "${constants.N_CS}"
        - check: "fc_response_time"
          min_time: 0
          max_time: "${constants.N_BS}"
    validation:
      type: "timing_check"
      comment: "验证时序要求符合ISO 14229标准"

# 后置条件
post_conditions:
  - description: "Reset ECU to default state"
    action:
      type: "reset_ecu"
      method: "soft_reset"
      
  - description: "Verify no DTCs generated"
    action:
      type: "check_dtc"
      expect: "no_dtc"

# 测试配置
test_config:
  retry_count: 3
  timeout_behavior: "fail_test"
  log_level: "detailed"
  capture_traces: true
  
# 预期结果
expected_results:
  - "ECU responds correctly to session requests"
  - "Multi-frame responses follow ISO-TP protocol"
  - "Block size handling works for all tested values (0-6)"
  - "Timing requirements are met"
  - "No unexpected DTCs are generated"
