# DSL2CAPL 语法指南

## 📋 概述

DSL2CAPL 使用 YAML 格式的领域特定语言(DSL)来描述汽车诊断测试用例。本指南详细说明了 DSL 的语法结构、支持的动作类型和参数配置。

**版本**: 1.0  
**更新日期**: 2025-07-30  
**适用范围**: 汽车诊断测试用例开发  

## 🏗️ 基本结构

DSL 文件采用 YAML 格式，包含以下主要部分：

```yaml
# 测试用例元数据
metadata:
  name: "测试用例名称"
  description: "测试用例描述"
  author: "作者"
  version: "版本号"
  test_id: "测试ID"

# 测试环境配置
environment:
  bus_type: "CAN"
  ecu_type: "APP"
  addressing: "PHYSICAL"
  baudrate: 500000
  session: "DEFAULT"

# 测试步骤
test_steps:
  - step: 1
    description: "步骤描述"
    action:
      type: "动作类型"
      # 动作参数...
```

## 📝 元数据配置 (metadata)

| 字段 | 类型 | 必需 | 描述 | 示例 |
|------|------|------|------|------|
| `name` | string | ✅ | 测试用例名称 | "TG01_TC01_BlockSizeHandling" |
| `description` | string | ✅ | 测试用例描述 | "Block size handling test" |
| `author` | string | ❌ | 作者信息 | "DSL2CAPL Generator" |
| `version` | string | ❌ | 版本号 | "1.0" |
| `test_id` | string | ❌ | 测试用例ID | "TG01_TC01_1021940" |

## 🌐 环境配置 (environment)

| 字段 | 类型 | 必需 | 可选值 | 描述 |
|------|------|------|--------|------|
| `bus_type` | string | ✅ | CAN, LIN, FlexRay | 总线类型 |
| `ecu_type` | string | ✅ | APP, BOOT, CALIB | ECU类型 |
| `addressing` | string | ✅ | PHYSICAL, FUNCTIONAL | 寻址模式 |
| `baudrate` | number | ❌ | 125000, 250000, 500000, 1000000 | 波特率 |
| `session` | string | ❌ | DEFAULT, PROGRAMMING, EXTENDED | 诊断会话 |

## 🎯 动作类型 (Action Types)

### 1. send_and_verify - 发送并验证

发送诊断请求并验证响应。

```yaml
action:
  type: "send_and_verify"
  send: "03 22 F1 86 00 00 00 00"
  expect: "04 62 F1 86 ** 00 00 00"
  timeout: 100
  comment: "确认ECU Session状态"
```

**参数说明**:
- `send` (string, 必需): 发送的十六进制数据，空格分隔
- `expect` (string, 可选): 期望的响应模式，支持通配符
- `timeout` (number, 可选): 超时时间(毫秒)，默认100ms
- `comment` (string, 可选): 步骤注释

**响应模式通配符**:
- `**`: 忽略该字节
- `1*`: 高4位必须是1，低4位忽略
- `*5`: 低4位必须是5，高4位忽略
- `AB`: 精确匹配0xAB

### 2. wait - 等待

等待指定时间。

```yaml
action:
  type: "wait"
  duration: 500
```

**参数说明**:
- `duration` (number, 必需): 等待时间(毫秒)

### 3. flow_control_sequence - 流控制序列

执行流控制序列，用于多帧传输。

```yaml
action:
  type: "flow_control_sequence"
  fc_frame: "30 01 00 00 00 00 00 00"
  expect_cf_pattern: "2* ** ** ** ** ** ** **"
  max_frames: 5
  comment: "BS=1流控制"
```

**参数说明**:
- `fc_frame` (string, 必需): 流控制帧数据
- `expect_cf_pattern` (string, 可选): 期望的连续帧模式
- `max_frames` (number, 可选): 期望的最大连续帧数
- `comment` (string, 可选): 步骤注释

### 4. repeat_flow_control - 重复流控制

重复执行流控制直到满足条件。

```yaml
action:
  type: "repeat_flow_control"
  fc_frame: "30 01 00 00 00 00 00 00"
  repeat_until: "complete_message"
  max_iterations: 20
```

**参数说明**:
- `fc_frame` (string, 必需): 流控制帧数据
- `repeat_until` (string, 必需): 重复条件
  - `"complete_message"`: 直到消息完整
  - `"timeout"`: 直到超时
- `max_iterations` (number, 可选): 最大重复次数，默认10

### 5. loop_flow_control - 循环流控制

循环测试不同的Block Size值。

```yaml
action:
  type: "loop_flow_control"
  bs_range: "2-6"
  fc_frame_template: "30 {BS} 00 00 00 00 00 00"
  base_request: "03 22 ED 20 00 00 00 00"
```

**参数说明**:
- `bs_range` (string, 必需): Block Size范围，格式"start-end"
- `fc_frame_template` (string, 必需): 流控制帧模板，{BS}会被替换
- `base_request` (string, 必需): 基础请求数据

## 📊 完整示例

```yaml
# Block Size处理测试用例
metadata:
  name: "TG01_TC01_BlockSizeHandling"
  description: "Block size handling physical addressing - Default session"
  author: "DSL2CAPL Generator"
  version: "1.0"
  test_id: "TG01_TC01_1021940"

environment:
  bus_type: "CAN"
  ecu_type: "APP"
  addressing: "PHYSICAL"
  baudrate: 500000
  session: "DEFAULT"

test_steps:
  - step: 1
    description: "确认ECU Session状态"
    action:
      type: "send_and_verify"
      send: "03 22 F1 86 00 00 00 00"
      expect: "04 62 F1 86 ** 00 00 00"
      timeout: 100
      comment: "To confirm ECU Session"

  - step: 2
    description: "发送多帧数据请求"
    action:
      type: "send_and_verify"
      send: "03 22 ED 20 00 00 00 00"
      expect: "1* ** 62 ED 20 ** ** **"
      timeout: 150
      comment: "Request multi-frame data"

  - step: 3
    description: "发送流控制帧(BS=0)并接收连续帧"
    action:
      type: "flow_control_sequence"
      fc_frame: "30 00 00 00 00 00 00 00"
      expect_cf_pattern: "2* ** ** ** ** ** ** **"
      max_frames: 15
      comment: "BS=0, ECU will send all CFs continuously"

  - step: 4
    description: "重复步骤5直到接收完整消息"
    action:
      type: "repeat_flow_control"
      fc_frame: "30 01 00 00 00 00 00 00"
      repeat_until: "complete_message"
      max_iterations: 20

  - step: 5
    description: "测试不同BS值(BS=2到6)"
    action:
      type: "loop_flow_control"
      bs_range: "2-6"
      fc_frame_template: "30 {BS} 00 00 00 00 00 00"
      base_request: "03 22 ED 20 00 00 00 00"
```

## ⚠️ 注意事项

### 数据格式
- 十六进制数据使用空格分隔: `"03 22 F1 86"`
- 大小写不敏感: `"AB CD"` 等同于 `"ab cd"`
- 支持不完整字节: `"3 22"` 会被解析为 `"03 22"`

### 通配符使用
- 通配符仅在响应验证中使用
- `**` 完全忽略该字节
- `*` 配合数字使用表示半字节匹配

### 性能考虑
- 超时时间建议根据实际ECU响应时间设置
- 重复操作的最大次数要合理设置避免死循环
- 大量数据传输时注意流控制参数

## 🔄 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0 | 2025-07-30 | 初始版本，支持基础诊断动作 |
| 1.1 | 2025-07-30 | 新增流控制相关动作类型 |

## 📞 技术支持

如有问题或建议，请联系开发团队或查看项目文档。
