// using System.ComponentModel.DataAnnotations; // 暂时移除外部依赖

namespace DSL2CAPL.Core.Models;

/// <summary>
/// 测试步骤
/// </summary>
public class TestStep
{
    /// <summary>
    /// 步骤编号
    /// </summary>
    // [Required] // 暂时移除外部依赖
    // [Range(1, int.MaxValue, ErrorMessage = "步骤编号必须大于0")] // 暂时移除外部依赖
    public int StepNumber { get; set; }

    /// <summary>
    /// 步骤描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 测试动作
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public TestAction Action { get; set; } = new SendAndVerifyAction();

    /// <summary>
    /// 验证规则
    /// </summary>
    public TestValidation? Validation { get; set; }

    /// <summary>
    /// 执行条件
    /// </summary>
    public TestConditions? Conditions { get; set; }

    /// <summary>
    /// 是否为关键步骤
    /// </summary>
    public bool IsCritical { get; set; } = false;

    /// <summary>
    /// 预期执行时间（毫秒）
    /// </summary>
    public int ExpectedDurationMs { get; set; } = 1000;
}

/// <summary>
/// 测试动作基类
/// </summary>
public abstract class TestAction
{
    /// <summary>
    /// 动作类型
    /// </summary>
    public abstract string ActionType { get; }

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public int TimeoutMs { get; set; } = 1000;

    /// <summary>
    /// 验证动作参数的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public abstract ValidationResult ValidateParameters();
}

/// <summary>
/// 发送并验证动作
/// </summary>
public class SendAndVerifyAction : TestAction
{
    public override string ActionType => "send_and_verify";

    /// <summary>
    /// 发送的帧数据
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public string SendFrame { get; set; } = string.Empty;

    /// <summary>
    /// 期望的响应帧
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public string ExpectFrame { get; set; } = string.Empty;

    /// <summary>
    /// 是否使用功能寻址
    /// </summary>
    public bool IsFunctional { get; set; } = false;

    /// <summary>
    /// 发送数据（别名，兼容性）
    /// </summary>
    public string SendData
    {
        get => SendFrame;
        set => SendFrame = value;
    }

    /// <summary>
    /// 期望响应（别名，兼容性）
    /// </summary>
    public string ExpectedResponse
    {
        get => ExpectFrame;
        set => ExpectFrame = value;
    }

    /// <summary>
    /// 超时时间（毫秒）
    /// </summary>
    public int TimeoutMs { get; set; } = 100;

    /// <summary>
    /// 注释
    /// </summary>
    public string Comment { get; set; } = string.Empty;

    public override ValidationResult ValidateParameters()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(SendFrame))
            errors.Add("发送帧数据不能为空");

        if (string.IsNullOrWhiteSpace(ExpectFrame))
            errors.Add("期望响应帧不能为空");

        // 验证帧格式（简单的十六进制格式检查）
        if (!IsValidHexFrame(SendFrame))
            errors.Add($"发送帧格式无效: {SendFrame}");

        if (!IsValidHexFrame(ExpectFrame))
            errors.Add($"期望帧格式无效: {ExpectFrame}");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private static bool IsValidHexFrame(string frame)
    {
        if (string.IsNullOrWhiteSpace(frame))
            return false;

        // 移除空格并检查是否为有效的十六进制字符串
        var cleanFrame = frame.Replace(" ", "").Replace("-", "");
        return cleanFrame.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f') || c == '*');
    }
}

/// <summary>
/// 流控制序列动作
/// </summary>
public class FlowControlSequenceAction : TestAction
{
    public override string ActionType => "flow_control_sequence";

    /// <summary>
    /// 流控制帧
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public string FcFrame { get; set; } = string.Empty;

    /// <summary>
    /// 期望的连续帧数量
    /// </summary>
    public string ExpectCfCount { get; set; } = "calculated_from_ff";

    /// <summary>
    /// 块大小列表
    /// </summary>
    public List<int> BlockSizes { get; set; } = new();

    /// <summary>
    /// 是否检查时序
    /// </summary>
    public bool CheckTiming { get; set; } = true;

    /// <summary>
    /// 流控制帧（别名，兼容性）
    /// </summary>
    public string FlowControlFrame
    {
        get => FcFrame;
        set => FcFrame = value;
    }

    /// <summary>
    /// 期望的连续帧模式（别名，兼容性）
    /// </summary>
    public string ExpectedConsecutiveFramePattern { get; set; } = string.Empty;

    /// <summary>
    /// 最大帧数
    /// </summary>
    public int MaxFrames { get; set; } = 0;

    /// <summary>
    /// 注释
    /// </summary>
    public string Comment { get; set; } = string.Empty;

    public override ValidationResult ValidateParameters()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(FcFrame))
            errors.Add("流控制帧不能为空");

        if (BlockSizes.Any(bs => bs < 0 || bs > 255))
            errors.Add("块大小必须在0-255范围内");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }
}

/// <summary>
/// 等待动作
/// </summary>
public class WaitAction : TestAction
{
    public override string ActionType => "wait";

    /// <summary>
    /// 等待时间（毫秒）
    /// </summary>
    // [Range(1, int.MaxValue, ErrorMessage = "等待时间必须大于0")] // 暂时移除外部依赖
    public int WaitTimeMs { get; set; } = 1000;

    /// <summary>
    /// 持续时间（别名，兼容性）
    /// </summary>
    public int DurationMs
    {
        get => WaitTimeMs;
        set => WaitTimeMs = value;
    }

    public override ValidationResult ValidateParameters()
    {
        var errors = new List<string>();

        if (WaitTimeMs <= 0)
            errors.Add("等待时间必须大于0");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }
}

/// <summary>
/// 测试验证规则
/// </summary>
public class TestValidation
{
    /// <summary>
    /// 验证类型
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public ValidationType Type { get; set; } = ValidationType.ExactMatch;

    /// <summary>
    /// 验证参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 是否启用时序检查
    /// </summary>
    public bool EnableTimingCheck { get; set; } = false;

    /// <summary>
    /// 最小响应时间（毫秒）
    /// </summary>
    public int MinResponseTimeMs { get; set; } = 0;

    /// <summary>
    /// 最大响应时间（毫秒）
    /// </summary>
    public int MaxResponseTimeMs { get; set; } = 5000;
}

/// <summary>
/// 验证类型枚举
/// </summary>
public enum ValidationType
{
    ExactMatch,
    PatternMatch,
    FrameCountMatch,
    TimingCheck,
    NoResponse
}

/// <summary>
/// 测试条件
/// </summary>
public class TestConditions
{
    /// <summary>
    /// 前置条件
    /// </summary>
    public List<string> Preconditions { get; set; } = new();

    /// <summary>
    /// 执行条件
    /// </summary>
    public string? ExecutionCondition { get; set; }

    /// <summary>
    /// 重试次数
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// 重试间隔（毫秒）
    /// </summary>
    public int RetryIntervalMs { get; set; } = 1000;
}

/// <summary>
/// 重复流控制动作
/// </summary>
public class RepeatFlowControlAction : TestAction
{
    /// <summary>
    /// 动作类型
    /// </summary>
    public override string ActionType => "repeat_flow_control";

    /// <summary>
    /// 流控制帧
    /// </summary>
    public string FcFrame { get; set; } = string.Empty;

    /// <summary>
    /// 重复条件
    /// </summary>
    public string RepeatUntil { get; set; } = string.Empty;

    /// <summary>
    /// 最大迭代次数
    /// </summary>
    public int MaxIterations { get; set; } = 10;

    public override ValidationResult ValidateParameters()
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(FcFrame))
            errors.Add("流控制帧不能为空");

        if (MaxIterations <= 0)
            errors.Add("最大迭代次数必须大于0");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }
}

/// <summary>
/// 循环流控制动作
/// </summary>
public class LoopFlowControlAction : TestAction
{
    /// <summary>
    /// 动作类型
    /// </summary>
    public override string ActionType => "loop_flow_control";

    /// <summary>
    /// BS范围，格式为 "start-end"，如 "2-6"
    /// </summary>
    public string BsRange { get; set; } = string.Empty;

    /// <summary>
    /// 流控制帧模板
    /// </summary>
    public string FcFrameTemplate { get; set; } = string.Empty;

    /// <summary>
    /// 基础请求
    /// </summary>
    public string BaseRequest { get; set; } = string.Empty;

    public override ValidationResult ValidateParameters()
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(BsRange))
            errors.Add("BS范围不能为空");
            
        // 验证BS范围格式
        if (!string.IsNullOrEmpty(BsRange))
        {
            var parts = BsRange.Split('-');
            if (parts.Length != 2 || 
                !int.TryParse(parts[0], out var start) || 
                !int.TryParse(parts[1], out var end) ||
                start <= 0 || end <= 0 || start > end)
            {
                errors.Add("BS范围格式无效，应为 \"start-end\"，如 \"2-6\"");
            }
        }

        if (string.IsNullOrEmpty(FcFrameTemplate))
            errors.Add("流控制帧模板不能为空");

        if (string.IsNullOrEmpty(BaseRequest))
            errors.Add("基础请求不能为空");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }
}
