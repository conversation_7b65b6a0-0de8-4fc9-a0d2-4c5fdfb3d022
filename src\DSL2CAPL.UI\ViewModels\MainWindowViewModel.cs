using DSL2CAPL.UI.Infrastructure;
using DSL2CAPL.UI.Services;
using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace DSL2CAPL.UI.ViewModels;

/// <summary>
/// 主窗口视图模型
/// </summary>
public class MainWindowViewModel : ObservableObject
{
    private readonly ILogger<MainWindowViewModel> _logger;
    private readonly IDialogService _dialogService;
    private readonly IFileService _fileService;
    private readonly ISettingsService _settingsService;
    private readonly BidirectionalConverter _converter;

    private string _windowTitle = "DSL2CAPL Converter v0.1.0";
    private string _dslContent = string.Empty;
    private string _caplContent = string.Empty;
    private string _statusMessage = "就绪";
    private int _currentLine = 1;
    private int _currentColumn = 1;
    private string _fileSize = "0 字节";
    private bool _isProcessing = false;
    private string _conversionTime = "";
    private bool _showErrorDialog = false;
    private string _errorMessage = "";
    private bool _isDirty = false;
    private string? _currentFilePath;

    public string WindowTitle
    {
        get => _windowTitle;
        set => SetProperty(ref _windowTitle, value);
    }

    public string DslContent
    {
        get => _dslContent;
        set => SetProperty(ref _dslContent, value);
    }

    public string CaplContent
    {
        get => _caplContent;
        set => SetProperty(ref _caplContent, value);
    }

    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    public int CurrentLine
    {
        get => _currentLine;
        set => SetProperty(ref _currentLine, value);
    }

    public int CurrentColumn
    {
        get => _currentColumn;
        set => SetProperty(ref _currentColumn, value);
    }

    public string FileSize
    {
        get => _fileSize;
        set => SetProperty(ref _fileSize, value);
    }

    public bool IsProcessing
    {
        get => _isProcessing;
        set => SetProperty(ref _isProcessing, value);
    }

    public string ConversionTime
    {
        get => _conversionTime;
        set => SetProperty(ref _conversionTime, value);
    }

    public bool ShowErrorDialog
    {
        get => _showErrorDialog;
        set => SetProperty(ref _showErrorDialog, value);
    }

    public string ErrorMessage
    {
        get => _errorMessage;
        set => SetProperty(ref _errorMessage, value);
    }

    public bool IsDirty
    {
        get => _isDirty;
        set => SetProperty(ref _isDirty, value);
    }

    public string? CurrentFilePath
    {
        get => _currentFilePath;
        set => SetProperty(ref _currentFilePath, value);
    }

    /// <summary>
    /// 最近文件列表
    /// </summary>
    public ObservableCollection<string> RecentFiles { get; } = new();

    /// <summary>
    /// 可用主题列表
    /// </summary>
    public ObservableCollection<string> AvailableThemes { get; } = new() { "Light", "Dark" };

    // 命令属性
    public ICommand NewFileCommand { get; private set; } = null!;
    public ICommand OpenFileCommand { get; private set; } = null!;
    public ICommand SaveFileCommand { get; private set; } = null!;
    public ICommand SaveAsFileCommand { get; private set; } = null!;
    public ICommand ExitCommand { get; private set; } = null!;
    public ICommand UndoCommand { get; private set; } = null!;
    public ICommand RedoCommand { get; private set; } = null!;
    public ICommand CutCommand { get; private set; } = null!;
    public ICommand CopyCommand { get; private set; } = null!;
    public ICommand PasteCommand { get; private set; } = null!;
    public ICommand FindCommand { get; private set; } = null!;
    public ICommand ReplaceCommand { get; private set; } = null!;
    public ICommand DslToCaplCommand { get; private set; } = null!;
    public ICommand CaplToDslCommand { get; private set; } = null!;
    public ICommand ValidateDslCommand { get; private set; } = null!;
    public ICommand PreviewConversionCommand { get; private set; } = null!;
    public ICommand SettingsCommand { get; private set; } = null!;
    public ICommand UserManualCommand { get; private set; } = null!;
    public ICommand ExamplesCommand { get; private set; } = null!;
    public ICommand AboutCommand { get; private set; } = null!;
    public ICommand CloseErrorDialogCommand { get; private set; } = null!;

    /// <summary>
    /// 构造函数
    /// </summary>
    public MainWindowViewModel(
        ILogger<MainWindowViewModel> logger,
        IDialogService dialogService,
        IFileService fileService,
        ISettingsService settingsService,
        BidirectionalConverter converter)
    {
        _logger = logger;
        _dialogService = dialogService;
        _fileService = fileService;
        _settingsService = settingsService;
        _converter = converter;

        // 初始化命令
        InitializeCommands();

        // 监听DSL内容变化
        PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(DslContent))
            {
                OnDslContentChanged();
            }
        };

        LoadRecentFiles();
        _logger.LogInformation("主窗口视图模型已初始化");
    }

    /// <summary>
    /// 简化构造函数（用于设计时）
    /// </summary>
    public MainWindowViewModel()
    {
        _logger = new ConsoleLogger<MainWindowViewModel>();
        _dialogService = new DialogService();
        _fileService = new FileService();
        _settingsService = new SettingsService();

        // 创建简单的转换器实例
        var dslParser = new SimpleDslParser();
        var caplGenerator = new SimpleCaplGenerator();
        _converter = new BidirectionalConverter(dslParser, caplGenerator);

        InitializeCommands();
        LoadRecentFiles();
    }

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        NewFileCommand = new RelayCommand(NewFile);
        OpenFileCommand = new RelayCommand(OpenFile);
        SaveFileCommand = new RelayCommand(SaveFileAction);
        SaveAsFileCommand = new RelayCommand(SaveAsFileAction);
        ExitCommand = new RelayCommand(Exit);

        UndoCommand = new RelayCommand(Undo);
        RedoCommand = new RelayCommand(Redo);
        CutCommand = new RelayCommand(Cut);
        CopyCommand = new RelayCommand(Copy);
        PasteCommand = new RelayCommand(Paste);
        FindCommand = new RelayCommand(Find);
        ReplaceCommand = new RelayCommand(Replace);

        DslToCaplCommand = new AsyncRelayCommand(DslToCapl);
        CaplToDslCommand = new AsyncRelayCommand(CaplToDsl);
        ValidateDslCommand = new AsyncRelayCommand(ValidateDsl);
        PreviewConversionCommand = new AsyncRelayCommand(PreviewConversion);

        SettingsCommand = new RelayCommand(Settings);
        UserManualCommand = new RelayCommand(UserManual);
        ExamplesCommand = new RelayCommand(Examples);
        AboutCommand = new RelayCommand(About);
        CloseErrorDialogCommand = new RelayCommand(CloseErrorDialog);
    }

    /// <summary>
    /// 窗口加载完成
    /// </summary>
    public void OnWindowLoaded()
    {
        StatusMessage = "应用程序已启动";
        _logger.LogInformation("主窗口已加载");
    }

    /// <summary>
    /// 窗口关闭前
    /// </summary>
    /// <returns>是否允许关闭</returns>
    public bool OnWindowClosing()
    {
        if (IsDirty)
        {
            var result = _dialogService.ShowMessageBox(
                "文件已修改，是否保存？",
                "确认",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);

            switch (result)
            {
                case MessageBoxResult.Yes:
                    return SaveFile();
                case MessageBoxResult.No:
                    return true;
                case MessageBoxResult.Cancel:
                    return false;
                default:
                    return false;
            }
        }

        return true;
    }

    /// <summary>
    /// DSL内容变化处理
    /// </summary>
    private void OnDslContentChanged()
    {
        IsDirty = true;
        UpdateFileSize();
        
        // TODO: 实时语法检查和预览更新
        // 这里可以添加防抖逻辑，避免频繁更新
    }

    /// <summary>
    /// 更新文件大小显示
    /// </summary>
    private void UpdateFileSize()
    {
        var bytes = System.Text.Encoding.UTF8.GetByteCount(DslContent);
        FileSize = bytes switch
        {
            < 1024 => $"{bytes} 字节",
            < 1024 * 1024 => $"{bytes / 1024.0:F1} KB",
            _ => $"{bytes / (1024.0 * 1024.0):F1} MB"
        };
    }

    /// <summary>
    /// 加载最近文件列表
    /// </summary>
    private void LoadRecentFiles()
    {
        // TODO: 从设置服务加载最近文件
        RecentFiles.Clear();
    }

    /// <summary>
    /// 保存文件
    /// </summary>
    /// <returns>是否保存成功</returns>
    private bool SaveFile()
    {
        try
        {
            if (string.IsNullOrEmpty(CurrentFilePath))
            {
                return SaveAsFile();
            }

            _fileService.WriteAllText(CurrentFilePath, DslContent);
            IsDirty = false;
            StatusMessage = $"文件已保存: {CurrentFilePath}";
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存文件失败");
            _dialogService.ShowMessageBox($"保存文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }
    }

    /// <summary>
    /// 另存为文件
    /// </summary>
    /// <returns>是否保存成功</returns>
    private bool SaveAsFile()
    {
        var filePath = _dialogService.ShowSaveFileDialog(
            "保存DSL文件",
            "DSL文件 (*.dsl)|*.dsl|所有文件 (*.*)|*.*");

        if (!string.IsNullOrEmpty(filePath))
        {
            CurrentFilePath = filePath;
            return SaveFile();
        }

        return false;
    }

    #region 命令实现

    private void NewFile()
    {
        if (IsDirty && !OnWindowClosing())
            return;

        DslContent = string.Empty;
        CaplContent = string.Empty;
        CurrentFilePath = null;
        IsDirty = false;
        StatusMessage = "新建文件";
        _logger.LogInformation("创建新文件");
    }

    private void OpenFile()
    {
        if (IsDirty && !OnWindowClosing())
            return;

        var filePath = _dialogService.ShowOpenFileDialog(
            "打开DSL文件",
            "DSL文件 (*.dsl)|*.dsl|所有文件 (*.*)|*.*");

        if (!string.IsNullOrEmpty(filePath))
        {
            try
            {
                DslContent = _fileService.ReadAllText(filePath);
                CurrentFilePath = filePath;
                IsDirty = false;
                StatusMessage = $"文件已打开: {filePath}";
                _logger.LogInformation("打开文件: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开文件失败: {FilePath}", filePath);
                _dialogService.ShowMessageBox($"打开文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void SaveFileAction()
    {
        SaveFile();
    }

    private void SaveAsFileAction()
    {
        SaveAsFile();
    }

    private void Exit()
    {
        System.Windows.Application.Current.Shutdown();
    }

    private void Undo()
    {
        // TODO: 实现撤销功能
        StatusMessage = "撤销操作";
    }

    private void Redo()
    {
        // TODO: 实现重做功能
        StatusMessage = "重做操作";
    }

    private void Cut()
    {
        // TODO: 实现剪切功能
        StatusMessage = "剪切";
    }

    private void Copy()
    {
        // TODO: 实现复制功能
        StatusMessage = "复制";
    }

    private void Paste()
    {
        // TODO: 实现粘贴功能
        StatusMessage = "粘贴";
    }

    private void Find()
    {
        // TODO: 实现查找功能
        StatusMessage = "查找";
    }

    private void Replace()
    {
        // TODO: 实现替换功能
        StatusMessage = "替换";
    }

    private async Task DslToCapl()
    {
        if (IsProcessing) return;

        var startTime = DateTime.Now;

        try
        {
            IsProcessing = true;
            StatusMessage = "正在转换DSL到CAPL...";

            if (string.IsNullOrWhiteSpace(DslContent))
            {
                ShowError("请先输入DSL内容");
                return;
            }

            // 使用转换器进行转换
            var result = await _converter.DslToCaplAsync(DslContent);

            var endTime = DateTime.Now;
            var elapsedMs = (endTime - startTime).TotalMilliseconds;
            ConversionTime = $"转换耗时: {elapsedMs:F0}ms";

            if (result.IsSuccess)
            {
                // 检查生成的CAPL代码是否有效
                if (string.IsNullOrWhiteSpace(result.ConvertedContent) || 
                    result.ConvertedContent.StartsWith("// 错误："))
                {
                    ShowError($"转换失败:\n{result.ConvertedContent}");
                    StatusMessage = "转换失败";
                    _logger.LogError("DSL转CAPL转换失败: {Content}", result.ConvertedContent);
                    return;
                }

                CaplContent = result.ConvertedContent;
                StatusMessage = $"DSL转换为CAPL完成 ({elapsedMs:F0}ms)";
                _logger.LogInformation("DSL转换为CAPL完成，耗时: {ElapsedMs}ms", elapsedMs);

                // 显示转换成功信息
                _dialogService.ShowMessageBox(
                    $"转换完成！\n\n" +
                    $"质量评分: {result.QualityScore}/100\n" +
                    $"耗时: {elapsedMs:F0}ms\n" +
                    $"生成代码行数: {CaplContent?.Split('\n').Length ?? 0}",
                    "转换成功",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            else
            {
                var errorMessage = string.Join("\n", result.Errors);
                ShowError($"转换失败:\n{errorMessage}");
                StatusMessage = "转换失败";
                _logger.LogError("DSL转CAPL转换失败: {Errors}", errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "DSL转CAPL转换失败");
            ShowError($"转换失败: {ex.Message}");
            StatusMessage = "转换失败";
        }
        finally
        {
            IsProcessing = false;
        }
    }

    /// <summary>
    /// 显示错误对话框
    /// </summary>
    private void ShowError(string message)
    {
        ErrorMessage = message;
        ShowErrorDialog = true;
    }

    /// <summary>
    /// 关闭错误对话框
    /// </summary>
    private void CloseErrorDialog()
    {
        ShowErrorDialog = false;
        ErrorMessage = "";
    }

    private async Task CaplToDsl()
    {
        try
        {
            StatusMessage = "正在转换CAPL到DSL...";

            // TODO: 实现CAPL到DSL的转换
            await Task.Delay(1000); // 模拟转换过程

            StatusMessage = "CAPL转换为DSL完成";
            _logger.LogInformation("CAPL转换为DSL完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CAPL转DSL转换失败");
            _dialogService.ShowMessageBox($"转换失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            StatusMessage = "转换失败";
        }
    }

    private async Task ValidateDsl()
    {
        try
        {
            StatusMessage = "正在验证DSL语法...";

            if (string.IsNullOrWhiteSpace(DslContent))
            {
                _dialogService.ShowMessageBox("请先输入DSL内容", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                StatusMessage = "就绪";
                return;
            }

            // 使用DSL解析器进行验证
            var dslParser = new SimpleDslParser();
            var result = await dslParser.ValidateSyntaxAsync(DslContent);

            if (result.IsValid)
            {
                StatusMessage = "DSL语法验证通过";
                _dialogService.ShowMessageBox("DSL语法验证通过！", "验证成功", MessageBoxButton.OK, MessageBoxImage.Information);
                _logger.LogInformation("DSL语法验证完成");
            }
            else
            {
                var errorMessage = string.Join("\n", result.Errors);
                var warningMessage = result.Warnings.Any() ? "\n\n警告:\n" + string.Join("\n", result.Warnings) : "";

                _dialogService.ShowMessageBox(
                    $"DSL语法验证失败:\n\n错误:\n{errorMessage}{warningMessage}",
                    "验证失败",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                StatusMessage = "DSL语法验证失败";
                _logger.LogWarning("DSL语法验证失败: {Errors}", errorMessage);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "DSL语法验证失败");
            _dialogService.ShowMessageBox($"验证失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            StatusMessage = "验证失败";
        }
    }

    private async Task PreviewConversion()
    {
        try
        {
            StatusMessage = "正在生成转换预览...";

            // TODO: 实现转换预览
            await Task.Delay(500); // 模拟预览生成过程

            StatusMessage = "转换预览已生成";
            _logger.LogInformation("转换预览已生成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成转换预览失败");
            _dialogService.ShowMessageBox($"预览失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            StatusMessage = "预览失败";
        }
    }

    private void Settings()
    {
        // TODO: 打开设置窗口
        StatusMessage = "打开设置";
    }

    private void UserManual()
    {
        // TODO: 打开用户手册
        StatusMessage = "打开用户手册";
    }

    private void Examples()
    {
        // TODO: 打开示例
        StatusMessage = "打开示例";
    }

    private void About()
    {
        _dialogService.ShowMessageBox(
            "DSL2CAPL Converter v0.1.0\n\n一个将DSL转换为CAPL代码的工具\n\n© 2024 开发团队",
            "关于",
            MessageBoxButton.OK,
            MessageBoxImage.Information);
    }

    #endregion
}
