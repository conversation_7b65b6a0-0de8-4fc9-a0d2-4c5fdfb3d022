// using System.ComponentModel.DataAnnotations; // 暂时移除外部依赖

namespace DSL2CAPL.Core.Models;

/// <summary>
/// 表示一个完整的测试用例
/// </summary>
public class TestCase
{
    /// <summary>
    /// 测试用例元信息
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public TestMetadata Metadata { get; set; } = new();

    /// <summary>
    /// 测试环境配置
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public TestEnvironment Environment { get; set; } = new();

    /// <summary>
    /// 测试数据定义
    /// </summary>
    public DataDefinitions? DataDefinitions { get; set; }

    /// <summary>
    /// 测试步骤列表
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public List<TestStep> TestSteps { get; set; } = new();

    /// <summary>
    /// 验证测试用例的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(Metadata.Name))
            errors.Add("测试用例名称不能为空");

        if (!TestSteps.Any())
            errors.Add("测试步骤不能为空");

        // 验证步骤编号的连续性
        var stepNumbers = TestSteps.Select(s => s.StepNumber).OrderBy(n => n).ToList();
        for (int i = 0; i < stepNumbers.Count; i++)
        {
            if (stepNumbers[i] != i + 1)
            {
                errors.Add($"测试步骤编号不连续，期望 {i + 1}，实际 {stepNumbers[i]}");
                break;
            }
        }

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }
}

/// <summary>
/// 测试用例元信息
/// </summary>
public class TestMetadata
{
    /// <summary>
    /// 测试用例名称
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 测试用例描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 测试组
    /// </summary>
    public string TestGroup { get; set; } = string.Empty;

    /// <summary>
    /// 作者
    /// </summary>
    public string Author { get; set; } = string.Empty;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime LastModified { get; set; } = DateTime.Now;
}

/// <summary>
/// 测试环境配置
/// </summary>
public class TestEnvironment
{
    /// <summary>
    /// 总线类型
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public BusType BusType { get; set; } = BusType.CAN;

    /// <summary>
    /// ECU类型
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public EcuType EcuType { get; set; } = EcuType.APP;

    /// <summary>
    /// 寻址方式
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public AddressingType AddressingType { get; set; } = AddressingType.Physical;

    /// <summary>
    /// 会话类型
    /// </summary>
    public SessionType SessionType { get; set; } = SessionType.Default;

    /// <summary>
    /// 波特率
    /// </summary>
    public int BaudRate { get; set; } = 500000;
}

/// <summary>
/// 总线类型枚举
/// </summary>
public enum BusType
{
    CAN,
    CANFD
}

/// <summary>
/// ECU类型枚举
/// </summary>
public enum EcuType
{
    APP,
    PBL,
    SBL
}

/// <summary>
/// 寻址方式枚举
/// </summary>
public enum AddressingType
{
    Physical,
    Functional
}

/// <summary>
/// 会话类型枚举
/// </summary>
public enum SessionType
{
    Default,
    Programming,
    Extended
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误信息列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 警告信息列表
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}
