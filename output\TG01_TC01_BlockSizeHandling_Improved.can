/*
 * Generated CAPL code from DSL (Improved Version with Common Library)
 * Test Case: TG01_TC01_BlockSizeHandling
 * Description: Block size handling physical addressing - Default session
 * Author: DSL2CAPL Generator
 * Version: 1.0
 * Generated on: 2025-07-30 15:52:00
 * Generator: DSL2CAPL Converter v0.1.0
 */

includes
{
  // Include DSL2CAPL Common Library
  #include "../CommonLibrary/Common.can"
}

variables
{
  // Test case metadata
  char gTestCaseName[100] = "TG01_TC01_BlockSizeHandling";
  char gTestDescription[200] = "Block size handling physical addressing - Default session";

  // Test configuration
  const dword DIAG_REQ_ID = 0x7E0;
  const dword DIAG_RES_ID = 0x7E8;
  const dword BAUDRATE = 500000;

  // Test variables
  int gCurrentStep = 0;
  int gTestResult = 1; // 1 = PASS, 0 = FAIL
}

/*
 * Test case: TG01_TC01_BlockSizeHandling
 * Description: Block size handling physical addressing - Default session
 */
testcase TC_TG01_TC01_BlockSizeHandling()
{
  // Initialize common library
  CommonLibrary_Initialize();

  // Initialize test
  TestCaseStart("TG01_TC01_BlockSizeHandling", "Block size handling physical addressing - Default session");

  // Step 1: 确认ECU Session状态
  gCurrentStep = 1;
  if (!Step1_ConfirmECUSession())
  {
    TestCaseFail("Step 1 failed: 确认ECU Session状态");
    return;
  }

  // Step 2: 发送多帧数据请求
  gCurrentStep = 2;
  if (!Step2_SendMultiFrameRequest())
  {
    TestCaseFail("Step 2 failed: 发送多帧数据请求");
    return;
  }

  // Step 3: 发送流控制帧(BS=0)并接收连续帧
  gCurrentStep = 3;
  if (!Step3_FlowControlBS0())
  {
    TestCaseFail("Step 3 failed: 发送流控制帧(BS=0)并接收连续帧");
    return;
  }

  // Step 4: 重新发送多帧数据请求
  gCurrentStep = 4;
  if (!Step4_ResendMultiFrameRequest())
  {
    TestCaseFail("Step 4 failed: 重新发送多帧数据请求");
    return;
  }

  // Step 5: 发送流控制帧(BS=1)并接收单个连续帧
  gCurrentStep = 5;
  if (!Step5_FlowControlBS1())
  {
    TestCaseFail("Step 5 failed: 发送流控制帧(BS=1)并接收单个连续帧");
    return;
  }

  // Step 6: 重复步骤5直到接收完整消息
  gCurrentStep = 6;
  if (!Step6_RepeatFlowControlUntilComplete())
  {
    TestCaseFail("Step 6 failed: 重复步骤5直到接收完整消息");
    return;
  }

  // Step 7: 测试不同BS值(BS=2到6)
  gCurrentStep = 7;
  if (!Step7_LoopFlowControlTest())
  {
    TestCaseFail("Step 7 failed: 测试不同BS值(BS=2到6)");
    return;
  }

  // Step 8: 最终确认ECU Session状态
  gCurrentStep = 8;
  if (!Step8_FinalECUSessionConfirm())
  {
    TestCaseFail("Step 8 failed: 最终确认ECU Session状态");
    return;
  }

  // Test completed successfully
  TestCasePass("All test steps completed successfully");
  
  // Cleanup
  CommonLibrary_Cleanup();
}

/*
 * Step 1: 确认ECU Session状态
 */
int Step1_ConfirmECUSession()
{
  TestStepStart(1, "确认ECU Session状态");
  
  // Use common diagnostic function
  byte reqData[] = {0x03, 0x22, 0xF1, 0x86, 0x00, 0x00, 0x00, 0x00};
  byte expPattern[] = {0x04, 0x62, 0xF1, 0x86, 0x00, 0x00, 0x00, 0x00}; // ** = 0x00 for simplicity
  byte expMask[] = {0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0xFF, 0xFF, 0xFF}; // ** = ignore
  
  int result = DiagCommon_SendAndVerifyPattern(reqData, 8, expPattern, expMask, 8, 100);
  
  if (result)
  {
    TestStepPass(1, "ECU Session confirmed successfully");
  }
  else
  {
    TestStepFail(1, "ECU Session confirmation failed");
  }
  
  return result;
}

/*
 * Step 2: 发送多帧数据请求
 */
int Step2_SendMultiFrameRequest()
{
  TestStepStart(2, "发送多帧数据请求");
  
  // Use common diagnostic function
  byte reqData[] = {0x03, 0x22, 0xED, 0x20, 0x00, 0x00, 0x00, 0x00};
  byte expPattern[] = {0x10, 0x00, 0x62, 0xED, 0x20, 0x00, 0x00, 0x00}; // 1* ** pattern
  byte expMask[] = {0xF0, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00}; // High nibble match for first byte
  
  int result = DiagCommon_SendAndVerifyPattern(reqData, 8, expPattern, expMask, 8, 150);
  
  if (result)
  {
    TestStepPass(2, "Multi-frame request successful");
  }
  else
  {
    TestStepFail(2, "Multi-frame request failed");
  }
  
  return result;
}

/*
 * Step 3: 发送流控制帧(BS=0)并接收连续帧
 */
int Step3_FlowControlBS0()
{
  TestStepStart(3, "发送流控制帧(BS=0)并接收连续帧");
  
  // Use common flow control function
  int result = DiagCommon_FlowControlSequence(0x30, 0x00, 0x00, 15, "2* ** ** ** ** ** ** **");
  
  if (result)
  {
    TestStepPass(3, "Flow control sequence (BS=0) completed");
  }
  else
  {
    TestStepFail(3, "Flow control sequence (BS=0) failed");
  }
  
  return result;
}

/*
 * Step 4: 重新发送多帧数据请求
 */
int Step4_ResendMultiFrameRequest()
{
  TestStepStart(4, "重新发送多帧数据请求");
  
  // Same as Step 2
  return Step2_SendMultiFrameRequest();
}

/*
 * Step 5: 发送流控制帧(BS=1)并接收单个连续帧
 */
int Step5_FlowControlBS1()
{
  TestStepStart(5, "发送流控制帧(BS=1)并接收单个连续帧");
  
  // Use common flow control function
  int result = DiagCommon_FlowControlSequence(0x30, 0x01, 0x00, 1, "2* ** ** ** ** ** ** **");
  
  if (result)
  {
    TestStepPass(5, "Flow control sequence (BS=1) completed");
  }
  else
  {
    TestStepFail(5, "Flow control sequence (BS=1) failed");
  }
  
  return result;
}

/*
 * Step 6: 重复步骤5直到接收完整消息
 */
int Step6_RepeatFlowControlUntilComplete()
{
  TestStepStart(6, "重复步骤5直到接收完整消息");
  
  // Use common repeat flow control function
  int result = DiagCommon_RepeatFlowControlUntilComplete(0x30, 0x01, 0x00, 20);
  
  if (result)
  {
    TestStepPass(6, "Repeat flow control completed successfully");
  }
  else
  {
    TestStepFail(6, "Repeat flow control failed");
  }
  
  return result;
}

/*
 * Step 7: 测试不同BS值(BS=2到6)
 */
int Step7_LoopFlowControlTest()
{
  TestStepStart(7, "测试不同BS值(BS=2到6)");
  
  // Use common loop flow control function
  byte baseReqData[] = {0x03, 0x22, 0xED, 0x20, 0x00, 0x00, 0x00, 0x00};
  int result = DiagCommon_LoopFlowControlTest(baseReqData, 8, 2, 6);
  
  if (result)
  {
    TestStepPass(7, "Loop flow control test completed successfully");
  }
  else
  {
    TestStepFail(7, "Loop flow control test failed");
  }
  
  return result;
}

/*
 * Step 8: 最终确认ECU Session状态
 */
int Step8_FinalECUSessionConfirm()
{
  TestStepStart(8, "最终确认ECU Session状态");
  
  // Same as Step 1
  return Step1_ConfirmECUSession();
}

/*
 * Message handlers are provided by Common Library
 * See MessageHandlers.can for implementation
 */
