Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DSL2CAPL.Core", "src\DSL2CAPL.Core\DSL2CAPL.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DSL2CAPL.UI", "src\DSL2CAPL.UI\DSL2CAPL.UI.csproj", "{8465573E-7344-4137-B418-2F740860AA52}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DSL2CAPL.Parser", "src\DSL2CAPL.Parser\DSL2CAPL.Parser.csproj", "{76A84066-86DF-476C-BF2C-7D0A9741AB6F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DSL2CAPL.Generator", "src\DSL2CAPL.Generator\DSL2CAPL.Generator.csproj", "{6DC40614-BFD1-4F3D-82F8-3ED6ED1740FE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DSL2CAPL.Data", "src\DSL2CAPL.Data\DSL2CAPL.Data.csproj", "{0304B632-153A-48E9-BEED-6B73D58F2C72}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DSL2CAPL.Tests", "tests\DSL2CAPL.Tests\DSL2CAPL.Tests.csproj", "{986074FD-A6F6-4ABA-A6EB-6F6A5F537684}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{11111111-1111-1111-1111-111111111111}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{22222222-2222-2222-2222-222222222222}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{33333333-3333-3333-3333-333333333333}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{44444444-4444-4444-4444-444444444444}"
	ProjectSection(SolutionItems) = preProject
		Document\Design.md = Document\Design.md
		README.md = README.md
		Document\Task.md = Document\Task.md
		过程要求.txt = 过程要求.txt
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{8465573E-7344-4137-B418-2F740860AA52}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8465573E-7344-4137-B418-2F740860AA52}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8465573E-7344-4137-B418-2F740860AA52}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8465573E-7344-4137-B418-2F740860AA52}.Release|Any CPU.Build.0 = Release|Any CPU
		{76A84066-86DF-476C-BF2C-7D0A9741AB6F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76A84066-86DF-476C-BF2C-7D0A9741AB6F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76A84066-86DF-476C-BF2C-7D0A9741AB6F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{76A84066-86DF-476C-BF2C-7D0A9741AB6F}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DC40614-BFD1-4F3D-82F8-3ED6ED1740FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DC40614-BFD1-4F3D-82F8-3ED6ED1740FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DC40614-BFD1-4F3D-82F8-3ED6ED1740FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DC40614-BFD1-4F3D-82F8-3ED6ED1740FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{0304B632-153A-48E9-BEED-6B73D58F2C72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0304B632-153A-48E9-BEED-6B73D58F2C72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0304B632-153A-48E9-BEED-6B73D58F2C72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0304B632-153A-48E9-BEED-6B73D58F2C72}.Release|Any CPU.Build.0 = Release|Any CPU
		{986074FD-A6F6-4ABA-A6EB-6F6A5F537684}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{986074FD-A6F6-4ABA-A6EB-6F6A5F537684}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{986074FD-A6F6-4ABA-A6EB-6F6A5F537684}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{986074FD-A6F6-4ABA-A6EB-6F6A5F537684}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890} = {11111111-1111-1111-1111-111111111111}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789012}
	EndGlobalSection
EndGlobal
