﻿using System;
using System.Globalization;
using System.Threading;
using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;
using DSL2CAPL.UI.Services;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🔧 测试错误处理改进...");

        // 测试正常的DSL文件
        await TestValidDsl();

        // 测试包含错误的DSL文件
        await TestInvalidDsl();

        Console.WriteLine("\n🎉 错误处理测试完成！");
    }

    static async Task TestValidDsl()
    {
        Console.WriteLine("\n=== 测试正常DSL文件 ===");
        try
        {

            // 测试正常的DSL内容
            var validDslContent = @"
metadata:
  name: ""Test Case TC01 BlockSizeHandling""
  description: ""Block size handling physical addressing - Default session""

environment:
  bus_type: ""CAN""
  ecu_type: ""APP""
  addressing: ""Physical""
  baudrate: 500000

test_steps:
- step: 1
  description: ""测试步骤""
  action:
    type: ""send_and_verify""
    send: ""03 22 F1 86 00 00 00 00""
    expect: ""04 62 F1 86 ** 00 00 00""
    timeout: 100
";

            // 创建转换器
            var dslParser = new SimpleDslParser();
            var caplGenerator = new SimpleCaplGenerator();
            var converter = new BidirectionalConverter(dslParser, caplGenerator);

            // 执行转换
            var result = await converter.DslToCaplAsync(validDslContent);

            if (result.IsSuccess)
            {
                Console.WriteLine("✓ 正常DSL转换成功");
                Console.WriteLine($"  质量评分: {result.QualityScore}");
                Console.WriteLine($"  耗时: {result.ElapsedMs}ms");
                Console.WriteLine($"  代码长度: {result.ConvertedContent.Length} 字符");
            }
            else
            {
                Console.WriteLine("❌ 正常DSL转换失败");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"  错误: {error}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 正常DSL测试异常: {ex.Message}");
        }
    }

    static async Task TestInvalidDsl()
    {
        Console.WriteLine("\n=== 测试错误DSL文件 ===");

        var testCases = new[]
        {
            new { Name = "无效总线类型", Content = @"
metadata:
  name: ""Test""
environment:
  bus_type: ""INVALID_BUS""
  ecu_type: ""APP""
  addressing: ""Physical""
  baudrate: 500000
test_steps:
- step: 1
  action:
    type: ""send_and_verify""
    send: ""03 22 F1 86""
    expect: ""04 62 F1 86""
" },
            new { Name = "缺少必需字段", Content = @"
metadata:
  name: ""Test""
environment:
  bus_type: ""CAN""
test_steps:
- step: 1
  action:
    type: ""send_and_verify""
    expect: ""04 62 F1 86""
" },
            new { Name = "无效动作类型", Content = @"
metadata:
  name: ""Test""
environment:
  bus_type: ""CAN""
  ecu_type: ""APP""
  addressing: ""Physical""
  baudrate: 500000
test_steps:
- step: 1
  action:
    type: ""invalid_action""
    send: ""03 22 F1 86""
    expect: ""04 62 F1 86""
" }
        };

        foreach (var testCase in testCases)
        {
            Console.WriteLine($"\n--- 测试: {testCase.Name} ---");
            try
            {
                var dslParser = new SimpleDslParser();
                var caplGenerator = new SimpleCaplGenerator();
                var converter = new BidirectionalConverter(dslParser, caplGenerator);

                var result = await converter.DslToCaplAsync(testCase.Content);

                if (result.IsSuccess)
                {
                    Console.WriteLine("⚠️  预期失败但转换成功");
                }
                else
                {
                    Console.WriteLine("✓ 正确检测到错误:");
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"  - {error}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✓ 正确抛出异常: {ex.Message}");
            }
        }
    }
}
