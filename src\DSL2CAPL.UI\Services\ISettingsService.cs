using System.IO;

namespace DSL2CAPL.UI.Services;

/// <summary>
/// 设置服务接口
/// </summary>
public interface ISettingsService
{
    /// <summary>
    /// 获取设置值
    /// </summary>
    /// <typeparam name="T">设置值类型</typeparam>
    /// <param name="key">设置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>设置值</returns>
    T GetSetting<T>(string key, T defaultValue = default!);

    /// <summary>
    /// 设置值
    /// </summary>
    /// <typeparam name="T">设置值类型</typeparam>
    /// <param name="key">设置键</param>
    /// <param name="value">设置值</param>
    void SetSetting<T>(string key, T value);

    /// <summary>
    /// 删除设置
    /// </summary>
    /// <param name="key">设置键</param>
    void RemoveSetting(string key);

    /// <summary>
    /// 检查设置是否存在
    /// </summary>
    /// <param name="key">设置键</param>
    /// <returns>是否存在</returns>
    bool HasSetting(string key);

    /// <summary>
    /// 保存设置到文件
    /// </summary>
    void SaveSettings();

    /// <summary>
    /// 从文件加载设置
    /// </summary>
    void LoadSettings();

    /// <summary>
    /// 重置所有设置为默认值
    /// </summary>
    void ResetToDefaults();

    /// <summary>
    /// 获取所有设置键
    /// </summary>
    /// <returns>设置键列表</returns>
    IEnumerable<string> GetAllKeys();
}

/// <summary>
/// 设置服务实现
/// </summary>
public class SettingsService : ISettingsService
{
    private readonly Dictionary<string, object> _settings = new();
    private readonly string _settingsFilePath;

    public SettingsService()
    {
        var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
        var appFolder = Path.Combine(appDataPath, "DSL2CAPL");
        Directory.CreateDirectory(appFolder);
        _settingsFilePath = Path.Combine(appFolder, "settings.json");
        
        LoadSettings();
    }

    public T GetSetting<T>(string key, T defaultValue = default!)
    {
        if (_settings.TryGetValue(key, out var value))
        {
            try
            {
                if (value is T directValue)
                    return directValue;

                // 尝试转换类型
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }

        return defaultValue;
    }

    public void SetSetting<T>(string key, T value)
    {
        _settings[key] = value!;
    }

    public void RemoveSetting(string key)
    {
        _settings.Remove(key);
    }

    public bool HasSetting(string key)
    {
        return _settings.ContainsKey(key);
    }

    public void SaveSettings()
    {
        try
        {
            var json = System.Text.Json.JsonSerializer.Serialize(_settings, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });
            File.WriteAllText(_settingsFilePath, json);
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常
            System.Diagnostics.Debug.WriteLine($"保存设置失败: {ex.Message}");
        }
    }

    public void LoadSettings()
    {
        try
        {
            if (File.Exists(_settingsFilePath))
            {
                var json = File.ReadAllText(_settingsFilePath);
                var settings = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                if (settings != null)
                {
                    _settings.Clear();
                    foreach (var kvp in settings)
                    {
                        _settings[kvp.Key] = kvp.Value;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            // 记录错误但不抛出异常
            System.Diagnostics.Debug.WriteLine($"加载设置失败: {ex.Message}");
        }
    }

    public void ResetToDefaults()
    {
        _settings.Clear();
        
        // 设置默认值
        SetSetting("Theme", "Light");
        SetSetting("FontSize", 12);
        SetSetting("FontFamily", "Consolas");
        SetSetting("AutoSave", true);
        SetSetting("AutoSaveInterval", 300);
        SetSetting("ShowLineNumbers", true);
        SetSetting("WordWrap", false);
        SetSetting("HighlightCurrentLine", true);
        SetSetting("ShowWhitespace", false);
        SetSetting("TabSize", 4);
        SetSetting("ConvertTabsToSpaces", true);
        SetSetting("MaxRecentFiles", 10);
        
        SaveSettings();
    }

    public IEnumerable<string> GetAllKeys()
    {
        return _settings.Keys;
    }
}

/// <summary>
/// 主题服务接口
/// </summary>
public interface IThemeService
{
    /// <summary>
    /// 当前主题
    /// </summary>
    string CurrentTheme { get; }

    /// <summary>
    /// 可用主题列表
    /// </summary>
    IEnumerable<string> AvailableThemes { get; }

    /// <summary>
    /// 应用主题
    /// </summary>
    /// <param name="themeName">主题名称</param>
    void ApplyTheme(string themeName);

    /// <summary>
    /// 主题变化事件
    /// </summary>
    event EventHandler<string>? ThemeChanged;
}

/// <summary>
/// 主题服务实现
/// </summary>
public class ThemeService : IThemeService
{
    private readonly ISettingsService _settingsService;
    private string _currentTheme;

    public ThemeService(ISettingsService settingsService)
    {
        _settingsService = settingsService;
        _currentTheme = _settingsService.GetSetting("Theme", "Light");
    }

    public string CurrentTheme => _currentTheme;

    public IEnumerable<string> AvailableThemes => new[] { "Light", "Dark" };

    public event EventHandler<string>? ThemeChanged;

    public void ApplyTheme(string themeName)
    {
        if (!AvailableThemes.Contains(themeName))
            return;

        _currentTheme = themeName;
        _settingsService.SetSetting("Theme", themeName);
        _settingsService.SaveSettings();

        // TODO: 实际应用主题到UI
        // 这里可以修改应用程序的资源字典

        ThemeChanged?.Invoke(this, themeName);
    }
}

/// <summary>
/// 编辑器服务接口
/// </summary>
public interface IEditorService
{
    /// <summary>
    /// 格式化代码
    /// </summary>
    /// <param name="code">代码内容</param>
    /// <param name="language">语言类型</param>
    /// <returns>格式化后的代码</returns>
    string FormatCode(string code, string language);

    /// <summary>
    /// 验证语法
    /// </summary>
    /// <param name="code">代码内容</param>
    /// <param name="language">语言类型</param>
    /// <returns>验证结果</returns>
    SyntaxValidationResult ValidateSyntax(string code, string language);

    /// <summary>
    /// 获取语法高亮定义
    /// </summary>
    /// <param name="language">语言类型</param>
    /// <returns>语法高亮定义</returns>
    object GetSyntaxHighlighting(string language);
}

/// <summary>
/// 语法验证结果
/// </summary>
public class SyntaxValidationResult
{
    public bool IsValid { get; set; }
    public List<SyntaxError> Errors { get; set; } = new();
    public List<SyntaxWarning> Warnings { get; set; } = new();
}

/// <summary>
/// 语法错误
/// </summary>
public class SyntaxError
{
    public string Message { get; set; } = string.Empty;
    public int Line { get; set; }
    public int Column { get; set; }
    public string ErrorCode { get; set; } = string.Empty;
}

/// <summary>
/// 语法警告
/// </summary>
public class SyntaxWarning
{
    public string Message { get; set; } = string.Empty;
    public int Line { get; set; }
    public int Column { get; set; }
    public string WarningCode { get; set; } = string.Empty;
}

/// <summary>
/// 编辑器服务实现
/// </summary>
public class EditorService : IEditorService
{
    public string FormatCode(string code, string language)
    {
        // TODO: 实现代码格式化
        return code;
    }

    public SyntaxValidationResult ValidateSyntax(string code, string language)
    {
        // TODO: 实现语法验证
        return new SyntaxValidationResult { IsValid = true };
    }

    public object GetSyntaxHighlighting(string language)
    {
        // TODO: 实现语法高亮定义
        return new object();
    }
}
