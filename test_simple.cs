using System;
using System.IO;
using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;
using DSL2CAPL.Core.Models;

class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            Console.WriteLine("=== DSL2CAPL 修复验证测试 ===");
            
            // 读取测试DSL文件
            string dslContent = File.ReadAllText("examples/test_fix.dsl");
            Console.WriteLine("✓ DSL文件读取成功");
            Console.WriteLine($"文件大小: {dslContent.Length} 字符");

            // 创建解析器
            var parser = new SimpleDslParser();
            Console.WriteLine("✓ 解析器创建成功");

            // 解析DSL
            Console.WriteLine("\n开始解析DSL...");
            var testCase = await parser.ParseAsync(dslContent);
            
            if (testCase == null)
            {
                Console.WriteLine("❌ DSL解析失败");
                return;
            }
            
            Console.WriteLine("✓ DSL解析成功！");
            Console.WriteLine($"测试用例名称: {testCase.Metadata.Name}");
            Console.WriteLine($"测试步骤数量: {testCase.TestSteps.Count}");

            // 验证每个步骤
            for (int i = 0; i < testCase.TestSteps.Count; i++)
            {
                var step = testCase.TestSteps[i];
                Console.WriteLine($"  步骤 {step.StepNumber}: {step.Description}");
                Console.WriteLine($"    动作类型: {step.Action.ActionType}");
            }

            // 创建生成器
            var generator = new SimpleCaplGenerator();
            Console.WriteLine("\n开始生成CAPL代码...");
            
            var caplCode = await generator.GenerateAsync(testCase);
            
            if (string.IsNullOrEmpty(caplCode) || caplCode.StartsWith("// 错误："))
            {
                Console.WriteLine("❌ CAPL代码生成失败");
                Console.WriteLine($"生成结果: {caplCode}");
                return;
            }
            
            Console.WriteLine("✓ CAPL代码生成成功！");
            Console.WriteLine($"生成的代码长度: {caplCode.Length} 字符");

            // 保存生成的代码
            File.WriteAllText("output/test_fix_result.can", caplCode);
            Console.WriteLine("✓ CAPL代码已保存到 output/test_fix_result.can");

            Console.WriteLine("\n🎉 修复验证成功！");
            Console.WriteLine("没有出现 'Input string was not in a correct format' 错误");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试过程中出现错误: {ex.Message}");
            Console.WriteLine($"错误类型: {ex.GetType().Name}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }
} 