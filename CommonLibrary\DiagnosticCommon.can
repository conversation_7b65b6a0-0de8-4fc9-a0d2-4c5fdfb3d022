/*
 * Diagnostic Common Library for DSL2CAPL
 * 诊断通用函数库
 * 
 * Purpose: 提供标准化的诊断测试公共方法，简化DSL到CAPL的转换
 * Author: DSL2CAPL Generator
 * Version: 1.0
 * Created: 2025-07-30
 */

#ifndef DIAGNOSTIC_COMMON_CAN
#define DIAGNOSTIC_COMMON_CAN

/*
 * 全局变量定义
 */
variables
{
  // 诊断通信配置
  const dword DIAG_DEFAULT_REQ_ID = 0x7E0;
  const dword DIAG_DEFAULT_RES_ID = 0x7E8;
  const dword DIAG_FUNCTIONAL_REQ_ID = 0x7DF;
  
  // 超时配置
  const dword DIAG_DEFAULT_TIMEOUT = 100;
  const dword DIAG_P2_TIMEOUT = 50;
  const dword DIAG_P2_STAR_TIMEOUT = 5000;
  
  // 流控制配置
  const byte DIAG_FC_CTS = 0x30;  // Continue To Send
  const byte DIAG_FC_WAIT = 0x31; // Wait
  const byte DIAG_FC_OVFLW = 0x32; // Overflow
  
  // 全局状态变量
  int gDiagResponseReceived = 0;
  message gDiagLastResponse;
  int gDiagConsecutiveFrameCount = 0;
  int gDiagMessageComplete = 0;
  byte gDiagReceivedData[4095]; // 最大诊断数据缓冲区
  int gDiagReceivedLength = 0;

  // 多帧消息处理变量
  byte gDiagFirstFrame[8];      // 首帧数据
  int gDiagFirstFrameLength = 0; // 首帧长度
  int gDiagExpectedTotalLength = 0; // 期望的总数据长度
  
  // 定时器
  timer diagResponseTimer;
  timer diagFlowControlTimer;
}

/*
 * 基础诊断发送和验证函数
 * @param reqData: 请求数据数组
 * @param reqLen: 请求数据长度
 * @param expPattern: 期望响应模式
 * @param expMask: 期望响应掩码 (0xFF=必须匹配, 0x00=忽略)
 * @param expLen: 期望响应长度
 * @param timeoutMs: 超时时间(毫秒)
 * @return: 1=成功, 0=失败
 */
int DiagCommon_SendAndVerifyPattern(byte reqData[], int reqLen, byte expPattern[], byte expMask[], int expLen, dword timeoutMs)
{
  message diagReq;
  int i;
  
  // 重置状态
  gDiagResponseReceived = 0;
  
  // 构造请求消息
  diagReq.id = DIAG_DEFAULT_REQ_ID;
  diagReq.dlc = (reqLen > 8) ? 8 : reqLen;
  
  for (i = 0; i < diagReq.dlc; i++)
  {
    diagReq.byte(i) = (i < reqLen) ? reqData[i] : 0x00;
  }
  
  // 发送请求
  output(diagReq);
  write("Sent diagnostic request: ID=0x%03X, DLC=%d", diagReq.id, diagReq.dlc);
  
  // 等待响应
  setTimer(diagResponseTimer, timeoutMs);
  while (!gDiagResponseReceived && testGetTimer(diagResponseTimer) > 0)
  {
    testWaitForTimeout(1);
  }
  
  // 验证响应
  if (gDiagResponseReceived)
  {
    return DiagCommon_VerifyResponsePattern(gDiagLastResponse, expPattern, expMask, expLen);
  }
  else
  {
    write("No diagnostic response received within %d ms", timeoutMs);
    return 0;
  }
}

/*
 * 响应模式验证函数
 * @param response: 接收到的响应消息
 * @param expPattern: 期望模式
 * @param expMask: 验证掩码
 * @param expLen: 期望长度
 * @return: 1=匹配, 0=不匹配
 */
int DiagCommon_VerifyResponsePattern(message response, byte expPattern[], byte expMask[], int expLen)
{
  int i;
  int matchCount = 0;
  
  write("Verifying response pattern...");
  
  for (i = 0; i < expLen && i < response.dlc; i++)
  {
    if (expMask[i] == 0xFF) // 需要精确匹配
    {
      if (response.byte(i) == expPattern[i])
      {
        matchCount++;
      }
      else
      {
        write("Pattern mismatch at byte %d: expected 0x%02X, got 0x%02X", i, expPattern[i], response.byte(i));
        return 0;
      }
    }
    else if (expMask[i] == 0xF0) // 高4位匹配
    {
      if ((response.byte(i) & 0xF0) == (expPattern[i] & 0xF0))
      {
        matchCount++;
      }
      else
      {
        write("High nibble mismatch at byte %d: expected 0x%X*, got 0x%02X", i, (expPattern[i] & 0xF0) >> 4, response.byte(i));
        return 0;
      }
    }
    // 0x00 = 忽略该字节
  }
  
  write("Response pattern verified successfully (%d bytes matched)", matchCount);
  return 1;
}

/*
 * 流控制序列处理函数
 * @param fcByte0: 流控制第一字节 (通常是0x30)
 * @param fcByte1: 流控制第二字节 (Block Size)
 * @param fcByte2: 流控制第三字节 (Separation Time)
 * @param maxFrames: 期望的最大连续帧数
 * @param cfPattern: 连续帧模式字符串
 * @return: 1=成功, 0=失败
 */
int DiagCommon_FlowControlSequence(byte fcByte0, byte fcByte1, byte fcByte2, int maxFrames, char cfPattern[])
{
  message fcFrame;
  int receivedFrames = 0;
  int i;
  
  // 重置连续帧计数
  gDiagConsecutiveFrameCount = 0;
  
  // 构造流控制帧
  fcFrame.id = DIAG_DEFAULT_REQ_ID;
  fcFrame.dlc = 8;
  fcFrame.byte(0) = fcByte0;
  fcFrame.byte(1) = fcByte1;
  fcFrame.byte(2) = fcByte2;
  for (i = 3; i < 8; i++)
  {
    fcFrame.byte(i) = 0x00;
  }
  
  // 发送流控制帧
  output(fcFrame);
  write("Sent flow control frame: 0x%02X 0x%02X 0x%02X", fcByte0, fcByte1, fcByte2);
  
  // 等待连续帧
  setTimer(diagFlowControlTimer, 1000);
  while (testGetTimer(diagFlowControlTimer) > 0)
  {
    if (gDiagConsecutiveFrameCount > receivedFrames)
    {
      receivedFrames = gDiagConsecutiveFrameCount;
      write("Received consecutive frame #%d", receivedFrames);
      
      // 检查是否达到期望帧数
      if (maxFrames > 0 && receivedFrames >= maxFrames)
      {
        break;
      }
    }
    testWaitForTimeout(10);
  }
  
  write("Flow control sequence completed: %d frames received (expected: %d)", receivedFrames, maxFrames);
  
  // 验证接收到的帧数
  if (maxFrames > 0)
  {
    return (receivedFrames >= maxFrames) ? 1 : 0;
  }
  else
  {
    return (receivedFrames > 0) ? 1 : 0;
  }
}

/*
 * 初始化多帧消息处理
 */
void DiagCommon_InitializeMultiFrame()
{
  int i;

  gDiagFirstFrameLength = 0;
  gDiagExpectedTotalLength = 0;
  gDiagConsecutiveFrameCount = 0;
  gDiagMessageComplete = 0;
  gDiagReceivedLength = 0;

  // 清空首帧缓冲区
  for (i = 0; i < 8; i++)
  {
    gDiagFirstFrame[i] = 0;
  }

  write("Multi-frame message processing initialized");
}

/*
 * 处理首帧数据
 * @param msg: 接收到的首帧消息
 * @return: 1=成功, 0=失败
 */
int DiagCommon_ProcessFirstFrame(message* msg)
{
  int i;
  byte pci;

  if (msg->dlc < 3)
  {
    write("Error: First frame too short (DLC=%d)", msg->dlc);
    return 0;
  }

  // 复制首帧数据
  gDiagFirstFrameLength = msg->dlc;
  for (i = 0; i < msg->dlc && i < 8; i++)
  {
    gDiagFirstFrame[i] = msg->byte(i);
  }

  pci = gDiagFirstFrame[0];

  // 检查是否为首帧 (1X格式)
  if ((pci & 0xF0) == 0x10)
  {
    // 获取总长度
    if (gDiagFirstFrameLength >= 3)
    {
      gDiagExpectedTotalLength = ((pci & 0x0F) << 8) | gDiagFirstFrame[1];
      write("First frame received: total length = %d bytes", gDiagExpectedTotalLength);

      // 重置计数器
      gDiagConsecutiveFrameCount = 0;
      gDiagMessageComplete = 0;

      return 1;
    }
  }

  write("Error: Invalid first frame PCI = 0x%02X", pci);
  return 0;
}

/*
 * 检查多帧消息是否完整
 * @return: 1=完整, 0=不完整
 */
int DiagCommon_IsMessageComplete()
{
  int receivedLength;

  // 检查是否有首帧数据
  if (gDiagFirstFrameLength == 0 || gDiagExpectedTotalLength == 0)
  {
    write("Warning: No first frame received yet");
    return 0;
  }

  // 计算已接收的数据长度
  // 首帧数据长度 (减去PCI和长度字节)
  receivedLength = gDiagFirstFrameLength - 2; // 减去PCI(1字节)和长度(1字节)

  // 加上所有连续帧的数据长度
  receivedLength += gDiagConsecutiveFrameCount * 7; // 每个连续帧最多7字节数据

  // 检查是否已接收完整消息
  if (receivedLength >= gDiagExpectedTotalLength)
  {
    write("Message complete: expected=%d, received=%d", gDiagExpectedTotalLength, receivedLength);
    gDiagMessageComplete = 1;
    return 1;
  }
  else
  {
    write("Message incomplete: expected=%d, received=%d, CF_count=%d",
          gDiagExpectedTotalLength, receivedLength, gDiagConsecutiveFrameCount);
    gDiagMessageComplete = 0;
    return 0;
  }
}

/*
 * 重复流控制直到消息完整
 * @param fcByte0: 流控制第一字节
 * @param fcByte1: 流控制第二字节 (Block Size)
 * @param fcByte2: 流控制第三字节
 * @param maxIterations: 最大重复次数
 * @return: 1=成功, 0=失败
 */
int DiagCommon_RepeatFlowControlUntilComplete(byte fcByte0, byte fcByte1, byte fcByte2, int maxIterations)
{
  int iteration = 0;
  int result = 0;
  
  gDiagMessageComplete = 0;
  
  while (!gDiagMessageComplete && iteration < maxIterations)
  {
    iteration++;
    write("Flow control iteration %d", iteration);
    
    result = DiagCommon_FlowControlSequence(fcByte0, fcByte1, fcByte2, fcByte1, "2* ** ** ** ** ** ** **");
    
    if (!result)
    {
      write("Flow control failed in iteration %d", iteration);
      return 0;
    }
    
    // 检查消息是否完整
    gDiagMessageComplete = DiagCommon_IsMessageComplete();
    
    if (gDiagMessageComplete)
    {
      write("Message completed after %d iterations", iteration);
      return 1;
    }
    
    testWaitForTimeout(50);
  }
  
  write("Message not completed after %d iterations", iteration);
  return 0;
}

/*
 * 循环测试不同的Block Size值
 * @param baseReqData: 基础请求数据
 * @param baseReqLen: 基础请求长度
 * @param startBS: 起始Block Size
 * @param endBS: 结束Block Size
 * @return: 1=成功, 0=失败
 */
int DiagCommon_LoopFlowControlTest(byte baseReqData[], int baseReqLen, int startBS, int endBS)
{
  int bs;
  int result;
  
  for (bs = startBS; bs <= endBS; bs++)
  {
    write("Testing Block Size = %d", bs);
    
    // 发送基础请求
    result = DiagCommon_SendAndVerifyPattern(baseReqData, baseReqLen, NULL, NULL, 0, 150);
    if (!result)
    {
      write("Base request failed for BS=%d", bs);
      continue;
    }
    
    testWaitForTimeout(50);
    
    // 执行流控制
    result = DiagCommon_FlowControlSequence(0x30, bs, 0x00, bs, "2* ** ** ** ** ** ** **");
    if (!result)
    {
      write("Flow control failed for BS=%d", bs);
      return 0;
    }
    
    testWaitForTimeout(100);
  }
  
  write("Loop flow control test completed successfully");
  return 1;
}

/*
 * 诊断模块初始化
 */
void DiagCommon_Initialize()
{
  write("Diagnostic Common Library initializing...");

  // 初始化全局变量
  gDiagResponseReceived = 0;
  gDiagReceivedLength = 0;

  // 初始化多帧消息处理
  DiagCommon_InitializeMultiFrame();

  write("Diagnostic Common Library initialized successfully");
}

/*
 * 诊断模块清理
 */
void DiagCommon_Cleanup()
{
  write("Diagnostic Common Library cleanup completed");
}

#endif // DIAGNOSTIC_COMMON_CAN
