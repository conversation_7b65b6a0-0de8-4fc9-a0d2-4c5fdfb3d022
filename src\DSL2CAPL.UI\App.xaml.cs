using DSL2CAPL.UI.ViewModels;
using DSL2CAPL.UI.Views;
using DSL2CAPL.UI.Services;
using DSL2CAPL.UI.Infrastructure;
using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;
using System.Windows;
using Application = System.Windows.Application;
using MessageBox = System.Windows.MessageBox;

namespace DSL2CAPL.UI;

/// <summary>
/// DSL2CAPL应用程序主类
/// </summary>
public partial class App : Application
{
    /// <summary>
    /// 应用程序启动时的初始化
    /// </summary>
    /// <param name="e">启动事件参数</param>
    protected override void OnStartup(StartupEventArgs e)
    {
        try
        {
            // 创建服务
            var logger = new ConsoleLogger<MainWindowViewModel>();
            var dialogService = new DialogService();
            var fileService = new FileService();
            var settingsService = new SettingsService();

            // 创建转换器
            var dslParser = new SimpleDslParser();
            var caplGenerator = new SimpleCaplGenerator();
            var converter = new BidirectionalConverter(dslParser, caplGenerator);

            // 创建主窗口和视图模型
            var viewModel = new MainWindowViewModel(logger, dialogService, fileService, settingsService, converter);
            var mainWindow = new MainWindow(viewModel);

            // 显示主窗口
            mainWindow.Show();

            base.OnStartup(e);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误");
            Shutdown(1);
        }
    }

}
