using DSL2CAPL.Core.Models;

namespace DSL2CAPL.Core.Services;

/// <summary>
/// 简化的转换结果
/// </summary>
public class ConversionResult
{
    /// <summary>
    /// 转换是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 转换后的内容
    /// </summary>
    public string ConvertedContent { get; set; } = string.Empty;

    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 质量评分（0-100）
    /// </summary>
    public int QualityScore { get; set; }

    /// <summary>
    /// 转换耗时（毫秒）
    /// </summary>
    public long ElapsedMs { get; set; }
}
