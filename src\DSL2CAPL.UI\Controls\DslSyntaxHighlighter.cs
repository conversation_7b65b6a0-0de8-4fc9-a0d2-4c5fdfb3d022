using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;

namespace DSL2CAPL.UI.Controls;

/// <summary>
/// DSL语法高亮器
/// </summary>
public class DslSyntaxHighlighter
{
    private static readonly Dictionary<string, System.Windows.Media.Brush> SyntaxColors = new()
    {
        { "keyword", System.Windows.Media.Brushes.Blue },
        { "string", System.Windows.Media.Brushes.Green },
        { "comment", System.Windows.Media.Brushes.Gray },
        { "number", System.Windows.Media.Brushes.Red },
        { "hex", System.Windows.Media.Brushes.Orange },
        { "property", System.Windows.Media.Brushes.Purple },
        { "value", System.Windows.Media.Brushes.DarkCyan }
    };

    private static readonly Dictionary<string, Regex> SyntaxPatterns = new()
    {
        { "comment", new Regex(@"#.*$", RegexOptions.Multiline) },
        { "keyword", new Regex(@"\b(metadata|environment|test_steps|step|description|action|type|send|expect|timeout|comment|fc_frame|expect_cf_pattern|max_frames|repeat_until|max_iterations|bs_range|fc_frame_template|base_request)\b") },
        { "string", new Regex(@"""[^""]*""") },
        { "hex", new Regex(@"\b[0-9A-Fa-f]{2}(\s+[0-9A-Fa-f]{2})*\b") },
        { "number", new Regex(@"\b\d+\b") },
        { "property", new Regex(@"^\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:", RegexOptions.Multiline) }
    };

    /// <summary>
    /// 对文本应用语法高亮
    /// </summary>
    public static void ApplySyntaxHighlighting(System.Windows.Controls.RichTextBox richTextBox, string text)
    {
        richTextBox.Document.Blocks.Clear();
        
        var paragraph = new Paragraph();
        var runs = CreateHighlightedRuns(text);
        
        foreach (var run in runs)
        {
            paragraph.Inlines.Add(run);
        }
        
        richTextBox.Document.Blocks.Add(paragraph);
    }

    /// <summary>
    /// 创建高亮的文本片段
    /// </summary>
    private static List<Run> CreateHighlightedRuns(string text)
    {
        var runs = new List<Run>();
        var segments = new List<TextSegment>();
        
        // 收集所有匹配的片段
        foreach (var pattern in SyntaxPatterns)
        {
            var matches = pattern.Value.Matches(text);
            foreach (Match match in matches)
            {
                segments.Add(new TextSegment
                {
                    Start = match.Index,
                    Length = match.Length,
                    Type = pattern.Key,
                    Text = match.Value
                });
            }
        }
        
        // 按位置排序
        segments.Sort((a, b) => a.Start.CompareTo(b.Start));
        
        // 处理重叠和创建Run对象
        int currentPos = 0;
        
        foreach (var segment in segments)
        {
            // 添加未高亮的文本
            if (segment.Start > currentPos)
            {
                var plainText = text.Substring(currentPos, segment.Start - currentPos);
                runs.Add(new Run(plainText));
            }
            
            // 添加高亮的文本
            var highlightedRun = new Run(segment.Text);
            if (SyntaxColors.ContainsKey(segment.Type))
            {
                highlightedRun.Foreground = SyntaxColors[segment.Type];
                
                if (segment.Type == "keyword")
                {
                    highlightedRun.FontWeight = FontWeights.Bold;
                }
            }
            
            runs.Add(highlightedRun);
            currentPos = segment.Start + segment.Length;
        }
        
        // 添加剩余的文本
        if (currentPos < text.Length)
        {
            var remainingText = text.Substring(currentPos);
            runs.Add(new Run(remainingText));
        }
        
        return runs;
    }

    /// <summary>
    /// 文本片段信息
    /// </summary>
    private class TextSegment
    {
        public int Start { get; set; }
        public int Length { get; set; }
        public string Type { get; set; } = "";
        public string Text { get; set; } = "";
    }
}

/// <summary>
/// DSL智能补全提供器
/// </summary>
public class DslIntelliSenseProvider
{
    private static readonly Dictionary<string, List<string>> CompletionItems = new()
    {
        { "root", new List<string> { "metadata:", "environment:", "test_steps:" } },
        { "metadata", new List<string> { "name:", "description:", "author:", "version:", "test_id:" } },
        { "environment", new List<string> { "bus_type:", "ecu_type:", "addressing:", "baudrate:", "session:" } },
        { "test_steps", new List<string> { "- step:", "description:", "action:" } },
        { "action", new List<string> { "type:", "send:", "expect:", "timeout:", "comment:", "fc_frame:", "expect_cf_pattern:", "max_frames:", "repeat_until:", "max_iterations:", "bs_range:", "fc_frame_template:", "base_request:" } },
        { "action_types", new List<string> { "send_and_verify", "flow_control_sequence", "repeat_flow_control", "loop_flow_control", "wait" } },
        { "bus_types", new List<string> { "CAN", "CANFD", "LIN", "ETHERNET" } },
        { "ecu_types", new List<string> { "APP", "BOOT", "DIAG" } },
        { "addressing", new List<string> { "PHYSICAL", "FUNCTIONAL" } },
        { "sessions", new List<string> { "DEFAULT", "EXTENDED", "PROGRAMMING" } }
    };

    /// <summary>
    /// 获取当前位置的补全建议
    /// </summary>
    public static List<string> GetCompletionSuggestions(string text, int caretPosition)
    {
        var suggestions = new List<string>();
        
        // 获取当前行
        var lines = text.Substring(0, caretPosition).Split('\n');
        var currentLine = lines.LastOrDefault() ?? "";
        
        // 分析上下文
        var context = AnalyzeContext(text, caretPosition);
        
        if (CompletionItems.ContainsKey(context))
        {
            suggestions.AddRange(CompletionItems[context]);
        }
        
        // 添加通用建议
        if (currentLine.Trim().EndsWith(":"))
        {
            suggestions.AddRange(new[] { "\"\"", "[]", "{}" });
        }
        
        return suggestions.Distinct().ToList();
    }

    /// <summary>
    /// 分析当前上下文
    /// </summary>
    private static string AnalyzeContext(string text, int caretPosition)
    {
        var lines = text.Substring(0, caretPosition).Split('\n');
        var currentLine = lines.LastOrDefault() ?? "";
        
        // 检查是否在特定块内
        for (int i = lines.Length - 1; i >= 0; i--)
        {
            var line = lines[i].Trim();
            
            if (line.StartsWith("metadata:"))
                return "metadata";
            else if (line.StartsWith("environment:"))
                return "environment";
            else if (line.StartsWith("test_steps:"))
                return "test_steps";
            else if (line.Contains("action:"))
                return "action";
            else if (line.Contains("type:"))
                return "action_types";
        }
        
        // 检查特定属性
        if (currentLine.Contains("bus_type:"))
            return "bus_types";
        else if (currentLine.Contains("ecu_type:"))
            return "ecu_types";
        else if (currentLine.Contains("addressing:"))
            return "addressing";
        else if (currentLine.Contains("session:"))
            return "sessions";
        
        return "root";
    }

    /// <summary>
    /// 格式化建议文本
    /// </summary>
    public static string FormatSuggestion(string suggestion, string context)
    {
        if (suggestion.EndsWith(":"))
        {
            return suggestion + " ";
        }
        
        if (context == "action_types" || context == "bus_types" || context == "ecu_types" || 
            context == "addressing" || context == "sessions")
        {
            return $"\"{suggestion}\"";
        }
        
        return suggestion;
    }
}
