# 完整的DSL测试示例
metadata:
  name: "CompleteTest"
  description: "完整的测试用例示例，包含多种动作类型"
  author: "DSL2CAPL"
  version: "1.0"

environment:
  bus_type: "CAN"
  ecu_type: "APP"
  addressing: "PHYSICAL"
  baudrate: 500000

test_steps:
  - step: 1
    description: "发送诊断请求并验证响应"
    action:
      type: "send_and_verify"
      send: "03 22 F1 86 00 00 00 00"
      expect: "04 62 F1 86 01 00 00 00"
      timeout: 100

  - step: 2
    description: "等待ECU处理"
    action:
      type: "wait"
      duration: 500

  - step: 3
    description: "发送多帧请求"
    action:
      type: "send_and_verify"
      send: "03 22 ED 20 00 00 00 00"
      expect: "10 15 62 ED 20 01 02 03"
      timeout: 150

  - step: 4
    description: "流控制序列测试"
    action:
      type: "flow_control_sequence"
      fc_frame: "30 00 00 00 00 00 00 00"
      expect_cf_pattern: "2* ** ** ** ** ** ** **"
