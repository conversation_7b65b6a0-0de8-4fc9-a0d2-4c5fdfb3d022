using DSL2CAPL.Core.Interfaces;
using DSL2CAPL.Core.Models;
using System.Text.RegularExpressions;
using ParseResult = DSL2CAPL.Core.Interfaces.ParseResult<DSL2CAPL.Core.Models.TestCase>;
using SyntaxSuggestion = DSL2CAPL.Core.Interfaces.SyntaxSuggestion;

namespace DSL2CAPL.Parser.DSL;

/// <summary>
/// 简化的DSL解析器实现
/// 支持基本的YAML格式解析
/// </summary>
public class SimpleDslParser
{
    /// <summary>
    /// 解析DSL文本为测试用例对象
    /// </summary>
    /// <param name="dslContent">DSL文本内容</param>
    /// <returns>解析结果</returns>
    public async Task<TestCase?> ParseAsync(string dslContent)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(dslContent))
            {
                return null;
            }

            // 解析DSL内容
            var testCase = ParseDslContent(dslContent);
            
            // 验证解析结果是否有效
            if (testCase == null || 
                string.IsNullOrWhiteSpace(testCase.Metadata.Name) ||
                testCase.TestSteps == null || 
                testCase.TestSteps.Count == 0)
            {
                return null;
            }
            
            return testCase;
        }
        catch (Exception)
        {
            return null;
        }
    }

    /// <summary>
    /// 验证DSL语法
    /// </summary>
    /// <param name="dslContent">DSL文本内容</param>
    /// <returns>验证结果</returns>
    public async Task<ValidationResult> ValidateSyntaxAsync(string dslContent)
    {
        var parseResult = await ParseAsync(dslContent);

        return new ValidationResult
        {
            IsValid = parseResult != null,
            Errors = parseResult == null ? new List<string> { "解析失败" } : new List<string>(),
            Warnings = new List<string>()
        };
    }



    /// <summary>
    /// 解析DSL内容为测试用例
    /// </summary>
    /// <param name="dslContent">DSL内容</param>
    /// <returns>测试用例对象</returns>
    private TestCase ParseDslContent(string dslContent)
    {
        var testCase = new TestCase();
        var lines = dslContent.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        
        string currentSection = "";
        var sectionContent = new List<string>();
        bool hasValidSections = false;

        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            
            // 跳过注释和空行
            if (trimmedLine.StartsWith("#") || string.IsNullOrWhiteSpace(trimmedLine))
                continue;

            // 检查是否是新的节
            // 只有顶级节才被识别为节（不包含缩进，且是已知的节名称）
            var knownSections = new[] { "metadata", "environment", "test_steps", "data_definitions" };
            var potentialSection = trimmedLine.TrimEnd(':').ToLower();

            if (trimmedLine.EndsWith(":") && !trimmedLine.StartsWith(" ") && !trimmedLine.StartsWith("-")
                && knownSections.Contains(potentialSection))
            {
                // 处理前一个节
                if (!string.IsNullOrEmpty(currentSection))
                {
                    ProcessSection(testCase, currentSection, sectionContent);
                }

                // 开始新节
                currentSection = trimmedLine.TrimEnd(':');
                sectionContent.Clear();
                hasValidSections = true;
            }
            else
            {
                sectionContent.Add(line);
            }
        }

        // 处理最后一个节
        if (!string.IsNullOrEmpty(currentSection))
        {
            ProcessSection(testCase, currentSection, sectionContent);
        }

        // 如果没有有效的节，返回null
        if (!hasValidSections)
        {
            return null;
        }

        return testCase;
    }

    /// <summary>
    /// 处理DSL节内容
    /// </summary>
    /// <param name="testCase">测试用例对象</param>
    /// <param name="sectionName">节名称</param>
    /// <param name="sectionContent">节内容</param>
    private void ProcessSection(TestCase testCase, string sectionName, List<string> sectionContent)
    {
        switch (sectionName.ToLower())
        {
            case "metadata":
                ProcessMetadataSection(testCase, sectionContent);
                break;
            case "environment":
                ProcessEnvironmentSection(testCase, sectionContent);
                break;
            case "data_definitions":
                ProcessDataDefinitionsSection(testCase, sectionContent);
                break;
            case "test_steps":
                ProcessTestStepsSection(testCase, sectionContent);
                break;
        }
    }

    /// <summary>
    /// 处理元数据节
    /// </summary>
    private void ProcessMetadataSection(TestCase testCase, List<string> content)
    {
        foreach (var line in content)
        {
            var trimmed = line.Trim();
            if (string.IsNullOrEmpty(trimmed)) continue;

            var parts = trimmed.Split(':', 2);
            if (parts.Length == 2)
            {
                var key = parts[0].Trim();
                var value = parts[1].Trim().Trim('"');

                switch (key.ToLower())
                {
                    case "name":
                        testCase.Metadata.Name = value;
                        break;
                    case "description":
                        testCase.Metadata.Description = value;
                        break;
                    case "author":
                        testCase.Metadata.Author = value;
                        break;
                    case "version":
                        testCase.Metadata.Version = value;
                        break;
                    case "test_group":
                        testCase.Metadata.TestGroup = value;
                        break;
                }
            }
        }
    }

    /// <summary>
    /// 处理环境节
    /// </summary>
    private void ProcessEnvironmentSection(TestCase testCase, List<string> content)
    {
        foreach (var line in content)
        {
            var trimmed = line.Trim();
            if (string.IsNullOrEmpty(trimmed)) continue;

            var parts = trimmed.Split(':', 2);
            if (parts.Length == 2)
            {
                var key = parts[0].Trim();
                var value = parts[1].Trim().Trim('"');

                switch (key.ToLower())
                {
                    case "bus_type":
                        if (Enum.TryParse<BusType>(value, true, out var busType))
                            testCase.Environment.BusType = busType;
                        break;
                    case "ecu_type":
                        if (Enum.TryParse<EcuType>(value, true, out var ecuType))
                            testCase.Environment.EcuType = ecuType;
                        break;
                    case "addressing":
                        if (Enum.TryParse<AddressingType>(value, true, out var addressingType))
                            testCase.Environment.AddressingType = addressingType;
                        break;
                    case "baudrate":
                        if (int.TryParse(value, out var baudrate) && baudrate > 0)
                            testCase.Environment.BaudRate = baudrate;
                        else
                            testCase.Environment.BaudRate = 500000; // 默认值
                        break;
                }
            }
        }
    }

    /// <summary>
    /// 处理数据定义节
    /// </summary>
    private void ProcessDataDefinitionsSection(TestCase testCase, List<string> content)
    {
        testCase.DataDefinitions = new DataDefinitions();
        // 简化实现，暂时跳过复杂的数据定义解析
    }

    /// <summary>
    /// 处理测试步骤节
    /// </summary>
    private void ProcessTestStepsSection(TestCase testCase, List<string> content)
    {
        var currentStep = new TestStep();
        bool inStep = false;
        bool inAction = false;
        var actionType = "";
        var actionData = new Dictionary<string, string>();

        foreach (var line in content)
        {
            var trimmed = line.Trim();
            if (string.IsNullOrEmpty(trimmed)) continue;



            if (trimmed.StartsWith("- step:"))
            {
                // 保存前一个步骤
                if (inStep && currentStep.StepNumber > 0)
                {
                    // 根据解析的动作数据创建动作对象
                    currentStep.Action = CreateActionFromData(actionType, actionData);
                    testCase.TestSteps.Add(currentStep);
                }

                // 开始新步骤
                currentStep = new TestStep();
                actionData.Clear();
                actionType = "";
                inAction = false;

                var stepMatch = Regex.Match(trimmed, @"- step:\s*(\d+)");
                if (stepMatch.Success)
                {
                    if (int.TryParse(stepMatch.Groups[1].Value, out var stepNumber))
                    {
                        currentStep.StepNumber = stepNumber;
                        inStep = true;
                    }
                }
            }
            else if (inStep && trimmed.StartsWith("description:"))
            {
                var descMatch = Regex.Match(trimmed, @"description:\s*""?([^""]+)""?");
                if (descMatch.Success)
                {
                    currentStep.Description = descMatch.Groups[1].Value;
                }
            }
            else if (inStep && trimmed.StartsWith("action:"))
            {
                inAction = true;
            }
            else if (inAction && trimmed.StartsWith("type:"))
            {
                var typeMatch = Regex.Match(trimmed, @"type:\s*""?([^""]+)""?");
                if (typeMatch.Success)
                {
                    actionType = typeMatch.Groups[1].Value;
                }
            }
            else if (inAction && trimmed.Contains(":") && !trimmed.StartsWith("type:"))
            {
                var parts = trimmed.Split(':', 2);
                if (parts.Length == 2)
                {
                    var key = parts[0].Trim();
                    var value = parts[1].Trim().Trim('"');
                    if (!string.IsNullOrEmpty(key) && !string.IsNullOrEmpty(value))
                    {
                        actionData[key] = value;
                    }
                }
            }
        }

        // 添加最后一个步骤
        if (inStep && currentStep.StepNumber > 0)
        {
            currentStep.Action = CreateActionFromData(actionType, actionData);
            testCase.TestSteps.Add(currentStep);
        }
    }

    /// <summary>
    /// 根据动作类型和数据创建动作对象
    /// </summary>
    private TestAction CreateActionFromData(string actionType, Dictionary<string, string> actionData)
    {
        switch (actionType.ToLower())
        {
            case "send_and_verify":
                var sendAction = new SendAndVerifyAction();
                if (actionData.ContainsKey("send"))
                    sendAction.SendData = actionData["send"];
                if (actionData.ContainsKey("expect"))
                    sendAction.ExpectedResponse = actionData["expect"];
                if (actionData.ContainsKey("timeout") && int.TryParse(actionData["timeout"], out var timeout) && timeout > 0)
                    sendAction.TimeoutMs = timeout;
                else
                    sendAction.TimeoutMs = 100; // 默认超时时间
                if (actionData.ContainsKey("comment"))
                    sendAction.Comment = actionData["comment"];
                return sendAction;

            case "wait":
                var waitAction = new WaitAction();
                if (actionData.ContainsKey("duration") && int.TryParse(actionData["duration"], out var duration) && duration > 0)
                    waitAction.DurationMs = duration;
                else
                    waitAction.DurationMs = 1000; // 默认等待时间
                return waitAction;

            case "flow_control_sequence":
                var flowAction = new FlowControlSequenceAction();
                if (actionData.ContainsKey("fc_frame"))
                    flowAction.FlowControlFrame = actionData["fc_frame"];
                if (actionData.ContainsKey("expect_cf_pattern"))
                    flowAction.ExpectedConsecutiveFramePattern = actionData["expect_cf_pattern"];
                if (actionData.ContainsKey("max_frames") && int.TryParse(actionData["max_frames"], out var maxFrames) && maxFrames > 0)
                    flowAction.MaxFrames = maxFrames;
                else
                    flowAction.MaxFrames = 1; // 默认最大帧数
                if (actionData.ContainsKey("comment"))
                    flowAction.Comment = actionData["comment"];
                return flowAction;

            case "repeat_flow_control":
                var repeatAction = new RepeatFlowControlAction();
                if (actionData.ContainsKey("fc_frame"))
                    repeatAction.FcFrame = actionData["fc_frame"];
                if (actionData.ContainsKey("repeat_until"))
                    repeatAction.RepeatUntil = actionData["repeat_until"];
                if (actionData.ContainsKey("max_iterations") && int.TryParse(actionData["max_iterations"], out var maxIter) && maxIter > 0)
                    repeatAction.MaxIterations = maxIter;
                else
                    repeatAction.MaxIterations = 10; // 默认最大迭代次数
                return repeatAction;

            case "loop_flow_control":
                var loopAction = new LoopFlowControlAction();
                if (actionData.ContainsKey("bs_range"))
                    loopAction.BsRange = actionData["bs_range"];
                if (actionData.ContainsKey("fc_frame_template"))
                    loopAction.FcFrameTemplate = actionData["fc_frame_template"];
                if (actionData.ContainsKey("base_request"))
                    loopAction.BaseRequest = actionData["base_request"];
                return loopAction;

            default:
                return new SendAndVerifyAction(); // 默认动作
        }
    }
}
