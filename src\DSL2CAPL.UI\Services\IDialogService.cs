using System.Windows;

namespace DSL2CAPL.UI.Services;

// 简化的枚举定义，避免依赖问题
public enum MessageBoxButton
{
    OK,
    OKCancel,
    YesNoCancel,
    YesNo
}

public enum MessageBoxImage
{
    None,
    Error,
    Question,
    Warning,
    Information
}

public enum MessageBoxResult
{
    None,
    OK,
    Cancel,
    Yes,
    No
}

/// <summary>
/// 对话框服务接口
/// </summary>
public interface IDialogService
{
    /// <summary>
    /// 显示消息框
    /// </summary>
    /// <param name="message">消息内容</param>
    /// <param name="title">标题</param>
    /// <param name="button">按钮类型</param>
    /// <param name="icon">图标类型</param>
    /// <returns>用户选择结果</returns>
    MessageBoxResult ShowMessageBox(string message, string title = "", MessageBoxButton button = MessageBoxButton.OK, MessageBoxImage icon = MessageBoxImage.None);

    /// <summary>
    /// 显示打开文件对话框
    /// </summary>
    /// <param name="title">对话框标题</param>
    /// <param name="filter">文件过滤器</param>
    /// <param name="initialDirectory">初始目录</param>
    /// <returns>选择的文件路径，如果取消则返回null</returns>
    string? ShowOpenFileDialog(string title = "打开文件", string filter = "所有文件 (*.*)|*.*", string? initialDirectory = null);

    /// <summary>
    /// 显示保存文件对话框
    /// </summary>
    /// <param name="title">对话框标题</param>
    /// <param name="filter">文件过滤器</param>
    /// <param name="initialDirectory">初始目录</param>
    /// <param name="defaultFileName">默认文件名</param>
    /// <returns>选择的文件路径，如果取消则返回null</returns>
    string? ShowSaveFileDialog(string title = "保存文件", string filter = "所有文件 (*.*)|*.*", string? initialDirectory = null, string? defaultFileName = null);

    /// <summary>
    /// 显示文件夹选择对话框
    /// </summary>
    /// <param name="title">对话框标题</param>
    /// <param name="initialDirectory">初始目录</param>
    /// <returns>选择的文件夹路径，如果取消则返回null</returns>
    string? ShowFolderBrowserDialog(string title = "选择文件夹", string? initialDirectory = null);

    /// <summary>
    /// 显示输入对话框
    /// </summary>
    /// <param name="message">提示消息</param>
    /// <param name="title">对话框标题</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>用户输入的值，如果取消则返回null</returns>
    string? ShowInputDialog(string message, string title = "输入", string defaultValue = "");

    /// <summary>
    /// 显示确认对话框
    /// </summary>
    /// <param name="message">确认消息</param>
    /// <param name="title">对话框标题</param>
    /// <returns>用户是否确认</returns>
    bool ShowConfirmDialog(string message, string title = "确认");

    /// <summary>
    /// 显示错误对话框
    /// </summary>
    /// <param name="message">错误消息</param>
    /// <param name="title">对话框标题</param>
    void ShowErrorDialog(string message, string title = "错误");

    /// <summary>
    /// 显示警告对话框
    /// </summary>
    /// <param name="message">警告消息</param>
    /// <param name="title">对话框标题</param>
    void ShowWarningDialog(string message, string title = "警告");

    /// <summary>
    /// 显示信息对话框
    /// </summary>
    /// <param name="message">信息消息</param>
    /// <param name="title">对话框标题</param>
    void ShowInfoDialog(string message, string title = "信息");
}

/// <summary>
/// 对话框服务实现
/// </summary>
public class DialogService : IDialogService
{
    public MessageBoxResult ShowMessageBox(string message, string title = "", MessageBoxButton button = MessageBoxButton.OK, MessageBoxImage icon = MessageBoxImage.None)
    {
        // 简化实现，避免WPF依赖问题
        Console.WriteLine($"MessageBox: {title} - {message}");
        return MessageBoxResult.OK;
    }

    public string? ShowOpenFileDialog(string title = "打开文件", string filter = "所有文件 (*.*)|*.*", string? initialDirectory = null)
    {
        // 简化实现，返回示例文件路径
        Console.WriteLine($"OpenFileDialog: {title}");
        return null; // 用户取消
    }

    public string? ShowSaveFileDialog(string title = "保存文件", string filter = "所有文件 (*.*)|*.*", string? initialDirectory = null, string? defaultFileName = null)
    {
        // 简化实现
        Console.WriteLine($"SaveFileDialog: {title}");
        return null; // 用户取消
    }

    public string? ShowFolderBrowserDialog(string title = "选择文件夹", string? initialDirectory = null)
    {
        // 简化实现
        Console.WriteLine($"FolderBrowserDialog: {title}");
        return null; // 用户取消
    }

    public string? ShowInputDialog(string message, string title = "输入", string defaultValue = "")
    {
        // 简化实现
        Console.WriteLine($"InputDialog: {title} - {message}");
        return defaultValue; // 返回默认值
    }

    public bool ShowConfirmDialog(string message, string title = "确认")
    {
        return ShowMessageBox(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes;
    }

    public void ShowErrorDialog(string message, string title = "错误")
    {
        ShowMessageBox(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
    }

    public void ShowWarningDialog(string message, string title = "警告")
    {
        ShowMessageBox(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
    }

    public void ShowInfoDialog(string message, string title = "信息")
    {
        ShowMessageBox(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
    }
}
