/***
@desc:   发送一个诊断CAN或CANfd帧，检查其响应是否符合预期
@param:  req[]      :发送的请求数据
         exptRes[]  :期待的响应数据
         outActRes[]:输出实际的响应数据
         timeoutMs: 发送Req后等待Res的时间
         isFuncReq  :是否为功能寻址  
@return: 1表示符合预期，0表示不符合预期
***/
/// <SendReqCheckRes>
byte SERVdocan_SendReqFrameCheckResFrame(int req[], int expRes[],dword timeoutMs,byte isFuncReq){
  int ret,i;
  dword reqID;
  APIutils_InitArray(gDoCAN_ActRes);
  if (isFuncReq) reqID = gDoCAN_FuncReqID;
  else reqID = gDoCAN_ReqID;
  ret=_canSendReqCheckRes(reqID,req,gDoCAN_ResID,expRes,gDoCAN_ActRes,timeoutMs,(gDoCAN_BusType==CANFD_BUS));
  if(ret==0){
    return 1;
  }
  APIutils_InitArray(gDoCAN_ReqStr);
  APIutils_TransIntArrayToString(gDoCAN_ReqStr,req,',',elcount(req));
  APIutils_InitArray(gDoCAN_ExpResStr);
  APIutils_TransIntArrayToString(gDoCAN_ExpResStr,expRes,',',elcount(expRes));
  while(gDoCAN_ActRes[0]==0x03 && gDoCAN_ActRes[1]==0x7F && gDoCAN_ActRes[3]==0x78){
    ret=_canCheckRes(gDoCAN_ResID,expRes,gDoCAN_ActRes,timeoutMs);
    if(ret<0){
      testStepFail("CheckRes","无响应! 请求[%s],期待响应[%s]",gDoCAN_ReqStr,gDoCAN_ExpResStr,ret);
      return 0;
    }
  }
  if (ret<0){
    testStepFail("CheckRes","无响应! 请求[%s],期待响应[%s]",gDoCAN_ReqStr,gDoCAN_ExpResStr);
    return 0;
  }
  APIutils_InitArray(gDoCAN_ResStr);
  APIutils_TransByteArrayToString(gDoCAN_ResStr,gDoCAN_ActRes,',',_min(elcount(gDoCAN_ActRes),8));
  if (ret>0){
    testStepFail("CheckRes","响应错误！请求[%s],期待响应[%s], 实际响应[%s]",gDoCAN_ReqStr,gDoCAN_ExpResStr,gDoCAN_ResStr);
    return 0;
  }
  //testStepPass("CheckRes","请求[%s],响应[%s],",gDoCAN_ReqStr,gDoCAN_ResStr);
  return 1;
}

/***
@desc:   发送诊断Request，获取诊断Response。
@param:  <reqStr>         诊断Request字符串
         <reqData>        诊断Request数据
         <reqLen>         诊断Request数据长度
         <o_resStr>       实际返回的Respose数据字符串
         <outResBytes>     实际返回的Respose数据
         <isFunctional>   是否以功能寻址方式发送数据，1表示以功能寻址方式发送诊断命令
         <timeoutMs>      等待Response的时间，单位是ms
@return: 返回的诊断response的长度         
***/
/// <API_DIAG/APIutils_DiagSendReqGetRes >
dword APIutils_DiagSendReqGetRes(char reqStr[],char o_resStr[],byte isFunctional,dword timeoutMs){
  byte resData[API_UTILS_DIAG_MAX_LEN];
  dword resLen;
  resLen=APIutils_DiagSendReqGetRes(reqStr,resData,isFunctional,timeoutMs);
  if(resLen==0)return 0;
  APIutils_TransByteArrayToString(o_resStr,resData,' ',resLen);
  return resLen;
}
/// <API_DIAG/APIutils_DiagSendReqGetRes >
dword APIutils_DiagSendReqGetRes(char reqStr[],byte outResBytes[],byte isFunctional,dword timeoutMs){
  byte reqData[API_UTILS_DIAG_MAX_LEN];
  int reqLen,i;
  reqLen=APIutils_TransStringToByteArray(reqData,reqStr);
  return APIutils_DiagSendReqGetRes(reqData,reqLen,outResBytes,isFunctional,timeoutMs);
}
/// <API_DIAG/APIutils_DiagSendReqGetRes >
dword APIutils_DiagSendReqGetRes(byte reqData[],dword reqLen,byte outResBytes[],byte isFunctional,dword timeoutMs){
  long result;
  dword i,resLen;
  diagRequest * diagReq;
  diagResponse * diagRes;
  float tReq,tRes;
  for(i=0;i<elcount(outResBytes);i++)outResBytes[i]=0;
  resLen=0;
  DiagResize(diagReq, reqLen);
  DiagSetPrimitiveData(diagReq, reqData, reqLen);
  if(isFunctional==1) 
    DiagSendFunctional(diagReq);
  else 
    DiagSendRequest(diagReq);
  if (TestWaitForDiagRequestSent(diagReq, 10000)!= 1)
  {
    TestStepFail("DiagSendFail","%f:Not get Request",timeNowFloat()/100);
    return 0;
  }
  if(reqData[0]!=gServiceIDnotPrinted)
  TestReportWriteDiagObject(diagReq);
  if(timeoutMs==0)return 0;
  tReq = APIutils_TimeNow();
  if(TestWaitForDiagResponse(diagReq, timeoutMs)<=0){
    return 0;
  }
  tRes = APIutils_TimeNow();
  result = DiagGetLastResponseCode(diagReq);
  if(result!=0){
    if(gP2serverMonitorServiceID>0 && reqData[0]==gP2serverMonitorServiceID && ((tRes-tReq)>gP2serverMonitorTimeout)){
      testStepFail("Fail","P2ServerTime Timeout, expect=25ms, actual=%.3f",tRes-tReq);
      write("[%.3f] P2ServerTime Timeout, expect=25ms, actual=%.3f",tRes,tRes-tReq);
      APIutils_PrintDataArrayToReport('f',"P2ServerTime",reqData,_min(10,reqLen));
    }
    if(reqData[0]!=gServiceIDnotPrinted)
    TestReportWriteDiagResponse(diagReq);
    DiagGetLastResponse(diagReq, diagRes);
    resLen=DiagGetPrimitiveData(diagRes, outResBytes, elcount(outResBytes));
  }
  return resLen;
}

/*******************************************************************************
                            API of Utility                                         
*******************************************************************************/
/***
@desc:   设置数组值，数组长度应当小于8
@param:  <array> 要设置的数组
         <d0-d7> 要填入数组的数据，如果数组长度小于8，会按照数组长度填入。
@return: -        
***/
/// <API_Utility/APIutils4net_SetArray >
void APIutils4net_SetArray(byte array[],byte b0,byte b1,byte b2,byte b3,byte b4,byte b5,byte b6,byte b7){
  if (elcount(array)>0) array[0]=b0;
  if (elcount(array)>1) array[1]=b1;
  if (elcount(array)>2) array[2]=b2;
  if (elcount(array)>3) array[3]=b3;
  if (elcount(array)>4) array[4]=b4;
  if (elcount(array)>5) array[5]=b5;
  if (elcount(array)>6) array[6]=b6;
  if (elcount(array)>7) array[7]=b7;
}
/// <API_Utility/APIutils4net_SetArray >
void APIutils4net_SetArray(int array[],int b0,int b1,int b2,int b3,int b4,int b5,int b6,int b7){
  if (elcount(array)>0) array[0]=b0;
  if (elcount(array)>1) array[1]=b1;
  if (elcount(array)>2) array[2]=b2;
  if (elcount(array)>3) array[3]=b3;
  if (elcount(array)>4) array[4]=b4;
  if (elcount(array)>5) array[5]=b5;
  if (elcount(array)>6) array[6]=b6;
  if (elcount(array)>7) array[7]=b7;
}

/***
@desc:   设置数组值，数组长度应当小于8
@param:  <array> 要设置的数组
         <d0-d7> 要填入数组的数据，如果数组长度小于8，会按照数组长度填入。
@return: -        
***/
/// <API_Utility/APIutils_SetArray >
void APIutils_SetArray(byte array[],byte b0,byte b1,byte b2,byte b3,byte b4,byte b5,byte b6,byte b7){
  APIutils4net_SetArray(array,b0,b1,b2,b3,b4,b5,b6,b7);
}
/// <API_Utility/APIutils_SetArray >
void APIutils_SetArray(int array[],int b0,int b1,int b2,int b3,int b4,int b5,int b6,int b7){
  APIutils4net_SetArray(array,b0,b1,b2,b3,b4,b5,b6,b7);
}

/***
@desc:   初始化数组，用特定数值填写数组 
@param:  <array> 要初始化的数组
         <value> 要填写到数组的特定数值，如果无此参数组填入0
@return: -        
***/
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(byte array[],byte value){
  APIutils4net_InitArray(array,value);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(byte array[]){
  APIutils_InitArray(array,0);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(char array[],char value){
  APIutils4net_InitArray(array,value);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(char array[]){
  APIutils_InitArray(array,0);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(int array[],int value){
  APIutils4net_InitArray(array,value);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(int array[]){
  APIutils_InitArray(array,0);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(float array[],float value){
  APIutils4net_InitArray(array,value);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(float array[]){
  APIutils_InitArray(array,0);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(dword array[],dword value){
  APIutils4net_InitArray(array,value);
}
/// <API_Utility/APIutils_InitArray >
void APIutils_InitArray(dword array[]){
  APIutils_InitArray(array,0);
}


/***
@desc:   切换ECU的Session，注意此函数不进行ECU的Session检查
@param:  session：1表示切换至DefaultSession
                  2表示切换至ProgramingSession
                  3表示切换至ExtendedSession
         isFuncReq: 是否功能寻址
         isDiagConsole: 是否使用诊断控制台进行切换
@return: 1表示参数没有问题，0表示参数有问题
***/
/// <Session>
byte SERVdocan_ChangeSession(byte destSession, byte isFuncReq, byte isDiagConsole){
  byte ret;
  if ( destSession<1 || destSession>3 ){
    testStepFail("ChangeSession","目标Session(0x%x)不支持！当前支持S01,S02,S03",destSession);
    return 0;
  }
  if(isDiagConsole){
    dword resLen;
    byte req[2]={0x10,0};
    byte res[8];
    req[1]=destSession;
    APIutils_InitArray(res);
    resLen=APIutils_DiagSendReqGetRes(req,elcount(req),res,isFuncReq,gDoCAN_P2time);
    if (res[0]!=0x50)return 0;
    if (res[1]!=destSession)return 0;
    ret = (resLen>0);
  }else{
    int req[8];
    int expRes[8];
    APIutils_InitArray(req,gDoCAN_IdleB);
    req[0]=2;
    req[1]=0x10;
    req[2]=destSession;
    APIutils_SetArray(expRes,-1,0x50,destSession,-1,-1,-1,-1,-1);
    ret= SERVdocan_SendReqFrameCheckResFrame(req,expRes,isFuncReq);
  }
  return ret;
}

/***
@desc:   Geely/Volvo用，进入APP模式 
@param:  -
@return: 1表示进入模式成功，0表示进入失败
***/
/// <SWDL/GEELY>
byte SERVdocan_GEELY_EnterAPPmode(){
  byte sChangeRet;
  TestStep("Geely","进入APP模式");
  sChangeRet=SERVdocan_ChangeSession(01);
  if(!sChangeRet){
    testStepFail("EnterAPP","进入01 Session失败");
    return 0;
  }
  testWaitForTimeout(100);
  return 1;
}

testfunction SUBCASE_TG01_TC01(int sessionResBytes[],byte isFunctional){
//TG01_TC01_1021940, TG02_TC01, TG03_TC03 
  dword resSize,resFrames,i,actCFframes;
  byte BS,seqNum;
  
	TestStep("(1)","Req:[03 22 F1 86 00 00 00 00]; Res:[04 62 F1 86 zz 00 00 00]");
	TestStep("","To confirm ECU Session");
  SERVdocan_SendReqFrameCheckResFrame(REQ_ReadDID_Session,sessionResBytes);
	
  TestStep("(2)","Req:[03 22 ED 20 00 00 00 00]; Res:[1* ** 62 ED 20 ** ** **]");
	TestStep("(3)","Req:[30 00 00 00 00 00 00 00]; Res:[21 ** ** ** ** ** ** **]");
	TestStep("","[22 ** ** ** ** ** ** **]");
	TestStep("","[23 ** ** ** ** ** ** **]");
	TestStep("","Number of frames sent by the DUT shall match FF_DL of FF N_PCI bytes");
  if (gSWtype==SW_PBL)
    SERVdocan_SendReqFrameCheckResFrame(DIDreqPBLresIsMF,DIDresIsMFofPBL,isFunctional);
  else if(gSWtype==SW_SBL)
    SERVdocan_SendReqFrameCheckResFrame(DIDreqSBLresIsMF,DIDresIsMFofSBL,isFunctional);
  else
    SERVdocan_SendReqFrameCheckResFrame(DIDreqAPPresIsMF,DIDresIsMFofAPP,isFunctional);
  resSize=SERVdocan_CheckResFrameType(FF);
  resFrames = APIutils_DiagCalcFramesNumber(resSize,8);
  SERVdocan_SendFCframeCheckCFframes(resFrames-1);
  SERVdocan_CheckNoResFrame(N_Cr);
	TestStep("(4)","Req:[03 22 ED 20 00 00 00 00]; Res:[1* ** 62 ED 20 ** ** **]");
	TestStep("(5)","Req:[30 01 00 00 00 00 00 00]; Res:[2N ** ** ** ** ** ** **]");
	TestStep("","Comment:BS=1, only one CFS shall be sent in the block.");  
	TestStep("(6)","Repeat step 5 until the complete message");
	TestStep("","have been received.");
	TestStep("","Number of frames sent by the DUT shall match FF_DL of");
	TestStep("","FF N_PCI bytes");
  TestStep("(7)","If BS<6");
	TestStep("","Jump tp step 5 and set BS=BS+1"); 
  for(BS=1;BS<=6;BS++)
  {
    seqNum = 0x21;
    actCFframes = 1;
    testStepPass("BS","Test BS = %d",BS);
    if (gSWtype==SW_PBL)
      SERVdocan_SendReqFrameCheckResFrame(DIDreqPBLresIsMF,DIDresIsMFofPBL,isFunctional);
    else if(gSWtype==SW_SBL)
      SERVdocan_SendReqFrameCheckResFrame(DIDreqSBLresIsMF,DIDresIsMFofSBL,isFunctional);
    else
      SERVdocan_SendReqFrameCheckResFrame(DIDreqAPPresIsMF,DIDresIsMFofAPP,isFunctional);
    for (i=1;i<resFrames;i++){
      byte ret,cnt;
      APIutils_CANsendMsg(DIAG_REQ_ID,0x30,BS,0x0,0,0,0,0,0);
      for(cnt=0;cnt<BS;cnt++)
      {
        APIutils_SetArray(resBytes,seqNum,-1,-1,-1,-1,-1,-1,-1);
        ret=SERVdocan_CheckResFrame(N_Cr,resBytes);
        if(!ret)break;
        if(++seqNum>=0x30)seqNum=0x20; 
        if(++actCFframes>=resFrames){
          seqNum=0;
          break;
        }
      }
      if(seqNum==0){//to jump to next BS
        SERVdocan_CheckNoResFrame(_max(N_Bs*1.05,N_Cr*1.05));
        break;
      }
      SERVdocan_CheckNoResFrame(_min(N_Bs*0.95,N_Cr*0.95));
    }
    testWaitForTimeout(1000);
  }
	TestStep("(8)","Req:[03 22 F1 86 00 00 00 00]; Res:[04 62 F1 86 zz 00 00 00]");
	TestStep("","To confirm ECU Session");
  SERVdocan_SendReqFrameCheckResFrame(REQ_ReadDID_Session,sessionResBytes);  
}


/***********************************
        Test Cases
***********************************/
/// <TG01 CAN Diagnostic server application>
testcase TG01_TC01_1021940() { //Block size handling physical addressing - Default session (Ver: 8)
	APIutils_RestartLog("../Log/TG01_TC01_1021940");
  TestCaseDescription("Description:\n");
	TestCaseDescription("物理01Session，ECU对BlockSize的处理\n");
  gSWtype = SW_APP;
  SERVdocan_GEELY_EnterAPPmode();
  diagDisconnectChannel(ECU_NAME_STR);
  SUBCASE_TG01_TC01(RES_ReadDID_Session01,0);
  diagConnectChannel(ECU_NAME_STR);
  testWaitForTimeout(6000);
}