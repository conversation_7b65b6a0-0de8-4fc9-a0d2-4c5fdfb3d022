using System.Text;
using System.IO;

namespace DSL2CAPL.UI.Services;

/// <summary>
/// 文件服务接口
/// </summary>
public interface IFileService
{
    /// <summary>
    /// 读取文件所有文本
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encoding">编码格式</param>
    /// <returns>文件内容</returns>
    string ReadAllText(string filePath, Encoding? encoding = null);

    /// <summary>
    /// 异步读取文件所有文本
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encoding">编码格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>文件内容</returns>
    Task<string> ReadAllTextAsync(string filePath, Encoding? encoding = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 写入文件所有文本
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">文件内容</param>
    /// <param name="encoding">编码格式</param>
    void WriteAllText(string filePath, string content, Encoding? encoding = null);

    /// <summary>
    /// 异步写入文件所有文本
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="content">文件内容</param>
    /// <param name="encoding">编码格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task WriteAllTextAsync(string filePath, string content, Encoding? encoding = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 读取文件所有行
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encoding">编码格式</param>
    /// <returns>文件行数组</returns>
    string[] ReadAllLines(string filePath, Encoding? encoding = null);

    /// <summary>
    /// 异步读取文件所有行
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="encoding">编码格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>文件行数组</returns>
    Task<string[]> ReadAllLinesAsync(string filePath, Encoding? encoding = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 写入文件所有行
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="lines">文件行</param>
    /// <param name="encoding">编码格式</param>
    void WriteAllLines(string filePath, IEnumerable<string> lines, Encoding? encoding = null);

    /// <summary>
    /// 异步写入文件所有行
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="lines">文件行</param>
    /// <param name="encoding">编码格式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>任务</returns>
    Task WriteAllLinesAsync(string filePath, IEnumerable<string> lines, Encoding? encoding = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查文件是否存在
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件是否存在</returns>
    bool FileExists(string filePath);

    /// <summary>
    /// 检查目录是否存在
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <returns>目录是否存在</returns>
    bool DirectoryExists(string directoryPath);

    /// <summary>
    /// 创建目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    void CreateDirectory(string directoryPath);

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="filePath">文件路径</param>
    void DeleteFile(string filePath);

    /// <summary>
    /// 复制文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="destinationFilePath">目标文件路径</param>
    /// <param name="overwrite">是否覆盖</param>
    void CopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false);

    /// <summary>
    /// 移动文件
    /// </summary>
    /// <param name="sourceFilePath">源文件路径</param>
    /// <param name="destinationFilePath">目标文件路径</param>
    void MoveFile(string sourceFilePath, string destinationFilePath);

    /// <summary>
    /// 获取文件信息
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件信息</returns>
    FileInfo GetFileInfo(string filePath);

    /// <summary>
    /// 获取目录中的文件
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="searchPattern">搜索模式</param>
    /// <param name="searchOption">搜索选项</param>
    /// <returns>文件路径数组</returns>
    string[] GetFiles(string directoryPath, string searchPattern = "*", SearchOption searchOption = SearchOption.TopDirectoryOnly);

    /// <summary>
    /// 获取目录中的子目录
    /// </summary>
    /// <param name="directoryPath">目录路径</param>
    /// <param name="searchPattern">搜索模式</param>
    /// <param name="searchOption">搜索选项</param>
    /// <returns>目录路径数组</returns>
    string[] GetDirectories(string directoryPath, string searchPattern = "*", SearchOption searchOption = SearchOption.TopDirectoryOnly);

    /// <summary>
    /// 获取文件扩展名
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件扩展名</returns>
    string GetFileExtension(string filePath);

    /// <summary>
    /// 获取文件名（不含扩展名）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件名</returns>
    string GetFileNameWithoutExtension(string filePath);

    /// <summary>
    /// 获取文件名（含扩展名）
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>文件名</returns>
    string GetFileName(string filePath);

    /// <summary>
    /// 获取目录路径
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <returns>目录路径</returns>
    string? GetDirectoryName(string filePath);

    /// <summary>
    /// 组合路径
    /// </summary>
    /// <param name="paths">路径部分</param>
    /// <returns>组合后的路径</returns>
    string CombinePath(params string[] paths);
}

/// <summary>
/// 文件服务实现
/// </summary>
public class FileService : IFileService
{
    public string ReadAllText(string filePath, Encoding? encoding = null)
    {
        return File.ReadAllText(filePath, encoding ?? Encoding.UTF8);
    }

    public async Task<string> ReadAllTextAsync(string filePath, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        return await File.ReadAllTextAsync(filePath, encoding ?? Encoding.UTF8, cancellationToken);
    }

    public void WriteAllText(string filePath, string content, Encoding? encoding = null)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        File.WriteAllText(filePath, content, encoding ?? Encoding.UTF8);
    }

    public async Task WriteAllTextAsync(string filePath, string content, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        await File.WriteAllTextAsync(filePath, content, encoding ?? Encoding.UTF8, cancellationToken);
    }

    public string[] ReadAllLines(string filePath, Encoding? encoding = null)
    {
        return File.ReadAllLines(filePath, encoding ?? Encoding.UTF8);
    }

    public async Task<string[]> ReadAllLinesAsync(string filePath, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        return await File.ReadAllLinesAsync(filePath, encoding ?? Encoding.UTF8, cancellationToken);
    }

    public void WriteAllLines(string filePath, IEnumerable<string> lines, Encoding? encoding = null)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        File.WriteAllLines(filePath, lines, encoding ?? Encoding.UTF8);
    }

    public async Task WriteAllLinesAsync(string filePath, IEnumerable<string> lines, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        await File.WriteAllLinesAsync(filePath, lines, encoding ?? Encoding.UTF8, cancellationToken);
    }

    public bool FileExists(string filePath) => File.Exists(filePath);

    public bool DirectoryExists(string directoryPath) => Directory.Exists(directoryPath);

    public void CreateDirectory(string directoryPath) => Directory.CreateDirectory(directoryPath);

    public void DeleteFile(string filePath) => File.Delete(filePath);

    public void CopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false)
    {
        File.Copy(sourceFilePath, destinationFilePath, overwrite);
    }

    public void MoveFile(string sourceFilePath, string destinationFilePath)
    {
        File.Move(sourceFilePath, destinationFilePath);
    }

    public FileInfo GetFileInfo(string filePath) => new(filePath);

    public string[] GetFiles(string directoryPath, string searchPattern = "*", SearchOption searchOption = SearchOption.TopDirectoryOnly)
    {
        return Directory.GetFiles(directoryPath, searchPattern, searchOption);
    }

    public string[] GetDirectories(string directoryPath, string searchPattern = "*", SearchOption searchOption = SearchOption.TopDirectoryOnly)
    {
        return Directory.GetDirectories(directoryPath, searchPattern, searchOption);
    }

    public string GetFileExtension(string filePath) => Path.GetExtension(filePath);

    public string GetFileNameWithoutExtension(string filePath) => Path.GetFileNameWithoutExtension(filePath);

    public string GetFileName(string filePath) => Path.GetFileName(filePath);

    public string? GetDirectoryName(string filePath) => Path.GetDirectoryName(filePath);

    public string CombinePath(params string[] paths) => Path.Combine(paths);
}
