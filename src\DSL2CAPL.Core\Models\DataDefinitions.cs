// using System.ComponentModel.DataAnnotations; // 暂时移除外部依赖

namespace DSL2CAPL.Core.Models;

/// <summary>
/// 测试数据定义
/// </summary>
public class DataDefinitions
{
    /// <summary>
    /// 常量定义
    /// </summary>
    public Dictionary<string, object> Constants { get; set; } = new();

    /// <summary>
    /// 帧定义
    /// </summary>
    public Dictionary<string, string> Frames { get; set; } = new();

    /// <summary>
    /// 变量定义
    /// </summary>
    public Dictionary<string, VariableDefinition> Variables { get; set; } = new();

    /// <summary>
    /// 消息ID定义
    /// </summary>
    public Dictionary<string, uint> MessageIds { get; set; } = new();

    /// <summary>
    /// 时序参数定义
    /// </summary>
    public TimingParameters TimingParameters { get; set; } = new();

    /// <summary>
    /// 验证数据定义的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        // 验证常量定义
        foreach (var constant in Constants)
        {
            if (string.IsNullOrWhiteSpace(constant.Key))
                errors.Add("常量名称不能为空");

            if (constant.Value == null)
                errors.Add($"常量 {constant.Key} 的值不能为空");
        }

        // 验证帧定义
        foreach (var frame in Frames)
        {
            if (string.IsNullOrWhiteSpace(frame.Key))
                errors.Add("帧名称不能为空");

            if (string.IsNullOrWhiteSpace(frame.Value))
                errors.Add($"帧 {frame.Key} 的数据不能为空");
            else if (!IsValidHexFrame(frame.Value))
                errors.Add($"帧 {frame.Key} 的数据格式无效: {frame.Value}");
        }

        // 验证变量定义
        foreach (var variable in Variables)
        {
            if (string.IsNullOrWhiteSpace(variable.Key))
                errors.Add("变量名称不能为空");

            var varValidation = variable.Value.Validate();
            if (!varValidation.IsValid)
                errors.AddRange(varValidation.Errors.Select(e => $"变量 {variable.Key}: {e}"));
        }

        // 验证消息ID
        foreach (var msgId in MessageIds)
        {
            if (string.IsNullOrWhiteSpace(msgId.Key))
                errors.Add("消息ID名称不能为空");

            if (msgId.Value == 0)
                warnings.Add($"消息ID {msgId.Key} 的值为0，请确认是否正确");
        }

        // 验证时序参数
        var timingValidation = TimingParameters.Validate();
        if (!timingValidation.IsValid)
            errors.AddRange(timingValidation.Errors);

        warnings.AddRange(timingValidation.Warnings);

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors,
            Warnings = warnings
        };
    }

    private static bool IsValidHexFrame(string frame)
    {
        if (string.IsNullOrWhiteSpace(frame))
            return false;

        // 移除空格并检查是否为有效的十六进制字符串
        var cleanFrame = frame.Replace(" ", "").Replace("-", "");
        return cleanFrame.All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f') || c == '*');
    }
}

/// <summary>
/// 变量定义
/// </summary>
public class VariableDefinition
{
    /// <summary>
    /// 变量类型
    /// </summary>
    // [Required] // 暂时移除外部依赖
    public VariableType Type { get; set; } = VariableType.Integer;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否为数组
    /// </summary>
    public bool IsArray { get; set; } = false;

    /// <summary>
    /// 数组大小（如果是数组）
    /// </summary>
    public int ArraySize { get; set; } = 1;

    /// <summary>
    /// 最小值（用于数值类型）
    /// </summary>
    public object? MinValue { get; set; }

    /// <summary>
    /// 最大值（用于数值类型）
    /// </summary>
    public object? MaxValue { get; set; }

    /// <summary>
    /// 验证变量定义的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();

        if (IsArray && ArraySize <= 0)
            errors.Add("数组大小必须大于0");

        // 验证默认值类型
        if (DefaultValue != null && !IsValidValueForType(DefaultValue, Type))
            errors.Add($"默认值类型与变量类型 {Type} 不匹配");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors
        };
    }

    private static bool IsValidValueForType(object value, VariableType type)
    {
        return type switch
        {
            VariableType.Integer => value is int or long or short or byte,
            VariableType.Float => value is float or double or decimal,
            VariableType.String => value is string,
            VariableType.Boolean => value is bool,
            VariableType.Byte => value is byte or int,
            _ => false
        };
    }
}

/// <summary>
/// 变量类型枚举
/// </summary>
public enum VariableType
{
    Integer,
    Float,
    String,
    Boolean,
    Byte
}

/// <summary>
/// 时序参数定义
/// </summary>
public class TimingParameters
{
    /// <summary>
    /// P2时间（毫秒）
    /// </summary>
    public int P2TimeMs { get; set; } = 50;

    /// <summary>
    /// P2扩展时间（毫秒）
    /// </summary>
    public int P2ExtTimeMs { get; set; } = 5000;

    /// <summary>
    /// N_Cr超时时间（毫秒）
    /// </summary>
    public int NCrTimeoutMs { get; set; } = 1000;

    /// <summary>
    /// N_Bs超时时间（毫秒）
    /// </summary>
    public int NBsTimeoutMs { get; set; } = 1000;

    /// <summary>
    /// N_Cs时间（毫秒）
    /// </summary>
    public int NCsTimeMs { get; set; } = 900;

    /// <summary>
    /// N_As时间（毫秒）
    /// </summary>
    public int NAsTimeMs { get; set; } = 1000;

    /// <summary>
    /// STmin最小时间（毫秒）
    /// </summary>
    public int STminMs { get; set; } = 0;

    /// <summary>
    /// 验证时序参数的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public ValidationResult Validate()
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        if (P2TimeMs <= 0)
            errors.Add("P2时间必须大于0");

        if (P2ExtTimeMs <= P2TimeMs)
            warnings.Add("P2扩展时间应该大于P2时间");

        if (NCrTimeoutMs <= 0)
            errors.Add("N_Cr超时时间必须大于0");

        if (NBsTimeoutMs <= 0)
            errors.Add("N_Bs超时时间必须大于0");

        if (NCsTimeMs <= 0)
            errors.Add("N_Cs时间必须大于0");

        if (NAsTimeMs <= 0)
            errors.Add("N_As时间必须大于0");

        if (STminMs < 0)
            errors.Add("STmin时间不能为负数");

        return new ValidationResult
        {
            IsValid = !errors.Any(),
            Errors = errors,
            Warnings = warnings
        };
    }
}
