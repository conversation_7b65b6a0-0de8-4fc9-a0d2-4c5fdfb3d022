using DSL2CAPL.Core.Models;
using FluentAssertions;
using Xunit;

namespace DSL2CAPL.Tests.Core;

/// <summary>
/// 测试用例模型的单元测试
/// </summary>
public class TestCaseTests
{
    [Fact]
    public void TestCase_DefaultConstructor_ShouldInitializeProperties()
    {
        // Arrange & Act
        var testCase = new TestCase();

        // Assert
        testCase.Metadata.Should().NotBeNull();
        testCase.Environment.Should().NotBeNull();
        testCase.TestSteps.Should().NotBeNull().And.BeEmpty();
        testCase.DataDefinitions.Should().BeNull();
    }

    [Fact]
    public void TestCase_WithValidData_ShouldValidateSuccessfully()
    {
        // Arrange
        var testCase = new TestCase
        {
            Metadata = new TestMetadata
            {
                Name = "Test Case 1",
                Description = "A test case for validation"
            },
            Environment = new TestEnvironment
            {
                BusType = BusType.CAN,
                EcuType = EcuType.APP
            },
            TestSteps = new List<TestStep>
            {
                new TestStep
                {
                    StepNumber = 1,
                    Description = "First step",
                    Action = new SendAndVerifyAction
                    {
                        SendFrame = "03 22 F1 86 00 00 00 00",
                        ExpectFrame = "04 62 F1 86 01 00 00 00"
                    }
                }
            }
        };

        // Act
        var result = testCase.Validate();

        // Assert
        result.IsValid.Should().BeTrue();
        result.Errors.Should().BeEmpty();
    }

    [Fact]
    public void TestCase_WithEmptyName_ShouldFailValidation()
    {
        // Arrange
        var testCase = new TestCase
        {
            Metadata = new TestMetadata
            {
                Name = "", // Empty name
                Description = "A test case"
            },
            TestSteps = new List<TestStep>
            {
                new TestStep
                {
                    StepNumber = 1,
                    Action = new SendAndVerifyAction
                    {
                        SendFrame = "03 22 F1 86 00 00 00 00",
                        ExpectFrame = "04 62 F1 86 01 00 00 00"
                    }
                }
            }
        };

        // Act
        var result = testCase.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("测试用例名称不能为空");
    }

    [Fact]
    public void TestCase_WithNoTestSteps_ShouldFailValidation()
    {
        // Arrange
        var testCase = new TestCase
        {
            Metadata = new TestMetadata
            {
                Name = "Test Case 1",
                Description = "A test case"
            },
            TestSteps = new List<TestStep>() // Empty steps
        };

        // Act
        var result = testCase.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain("测试步骤不能为空");
    }

    [Fact]
    public void TestCase_WithNonConsecutiveStepNumbers_ShouldFailValidation()
    {
        // Arrange
        var testCase = new TestCase
        {
            Metadata = new TestMetadata
            {
                Name = "Test Case 1",
                Description = "A test case"
            },
            TestSteps = new List<TestStep>
            {
                new TestStep
                {
                    StepNumber = 1,
                    Action = new SendAndVerifyAction
                    {
                        SendFrame = "03 22 F1 86 00 00 00 00",
                        ExpectFrame = "04 62 F1 86 01 00 00 00"
                    }
                },
                new TestStep
                {
                    StepNumber = 3, // Skip step 2
                    Action = new SendAndVerifyAction
                    {
                        SendFrame = "03 22 F1 86 00 00 00 00",
                        ExpectFrame = "04 62 F1 86 01 00 00 00"
                    }
                }
            }
        };

        // Act
        var result = testCase.Validate();

        // Assert
        result.IsValid.Should().BeFalse();
        result.Errors.Should().Contain(e => e.Contains("测试步骤编号不连续"));
    }

    [Theory]
    [InlineData(BusType.CAN)]
    [InlineData(BusType.CANFD)]
    public void TestEnvironment_WithValidBusType_ShouldSetCorrectly(BusType busType)
    {
        // Arrange & Act
        var environment = new TestEnvironment
        {
            BusType = busType
        };

        // Assert
        environment.BusType.Should().Be(busType);
    }

    [Theory]
    [InlineData(EcuType.APP)]
    [InlineData(EcuType.PBL)]
    [InlineData(EcuType.SBL)]
    public void TestEnvironment_WithValidEcuType_ShouldSetCorrectly(EcuType ecuType)
    {
        // Arrange & Act
        var environment = new TestEnvironment
        {
            EcuType = ecuType
        };

        // Assert
        environment.EcuType.Should().Be(ecuType);
    }

    [Theory]
    [InlineData(AddressingType.Physical)]
    [InlineData(AddressingType.Functional)]
    public void TestEnvironment_WithValidAddressingType_ShouldSetCorrectly(AddressingType addressingType)
    {
        // Arrange & Act
        var environment = new TestEnvironment
        {
            AddressingType = addressingType
        };

        // Assert
        environment.AddressingType.Should().Be(addressingType);
    }

    [Fact]
    public void TestMetadata_DefaultValues_ShouldBeSetCorrectly()
    {
        // Arrange & Act
        var metadata = new TestMetadata();

        // Assert
        metadata.Name.Should().BeEmpty();
        metadata.Description.Should().BeEmpty();
        metadata.TestGroup.Should().BeEmpty();
        metadata.Author.Should().BeEmpty();
        metadata.Version.Should().Be("1.0");
        metadata.CreatedAt.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
        metadata.LastModified.Should().BeCloseTo(DateTime.Now, TimeSpan.FromSeconds(1));
    }
}
