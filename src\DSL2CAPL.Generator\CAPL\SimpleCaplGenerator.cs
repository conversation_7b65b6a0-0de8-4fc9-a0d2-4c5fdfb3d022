using DSL2CAPL.Core.Interfaces;
using DSL2CAPL.Core.Models;
using System.Text;
using System.Text.RegularExpressions;
using System.Globalization;
using GenerationResult = DSL2CAPL.Core.Interfaces.GenerationResult;

namespace DSL2CAPL.Generator.CAPL;

/// <summary>
/// 简化的CAPL代码生成器
/// </summary>
public class SimpleCaplGenerator
{
    /// <summary>
    /// 从测试用例生成CAPL代码
    /// </summary>
    /// <param name="testCase">测试用例</param>
    /// <returns>生成的CAPL代码</returns>
    public async Task<string> GenerateAsync(TestCase testCase)
    {
        try
        {
            if (testCase == null)
            {
                return "// 错误：测试用例不能为空";
            }

            // 验证测试用例内容是否有效
            if (string.IsNullOrWhiteSpace(testCase.Metadata?.Name) ||
                testCase.TestSteps == null || 
                testCase.TestSteps.Count == 0)
            {
                return "// 错误：无有效测试内容，请检查DSL格式";
            }

            // 生成CAPL代码
            var caplCode = GenerateCaplCode(testCase);
            return caplCode;
        }
        catch (Exception ex)
        {
            return $"// 错误：代码生成失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 生成CAPL代码
    /// </summary>
    /// <param name="testCase">测试用例</param>
    /// <returns>CAPL代码</returns>
    private string GenerateCaplCode(TestCase testCase)
    {
        var sb = new StringBuilder();

        // 生成文件头注释
        GenerateFileHeader(sb, testCase);

        // 生成includes
        GenerateIncludes(sb);

        // 生成变量声明
        GenerateVariables(sb, testCase);

        // 生成主测试函数
        GenerateMainTestFunction(sb, testCase);

        // 生成测试步骤函数
        GenerateTestStepFunctions(sb, testCase);

        // 生成消息处理器
        GenerateMessageHandlers(sb, testCase);

        // 生成辅助函数
        GenerateHelperFunctions(sb, testCase);

        return sb.ToString();
    }

    /// <summary>
    /// 生成文件头注释
    /// </summary>
    private void GenerateFileHeader(StringBuilder sb, TestCase testCase)
    {
        sb.AppendLine("/*");
        sb.AppendLine(" * Generated CAPL code from DSL");
        sb.AppendLine($" * Test Case: {testCase.Metadata.Name}");
        sb.AppendLine($" * Description: {testCase.Metadata.Description}");
        sb.AppendLine($" * Author: {testCase.Metadata.Author}");
        sb.AppendLine($" * Version: {testCase.Metadata.Version}");
        sb.AppendLine($" * Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine(" * Generator: DSL2CAPL Converter v0.1.0");
        sb.AppendLine(" */");
        sb.AppendLine();
    }

    /// <summary>
    /// 生成includes
    /// </summary>
    private void GenerateIncludes(StringBuilder sb)
    {
        sb.AppendLine("includes");
        sb.AppendLine("{");
        sb.AppendLine("  // Include DSL2CAPL Common Library");
        sb.AppendLine("  #include \"../CommonLibrary/Common.can\"");
        sb.AppendLine("}");
        sb.AppendLine();
    }

    /// <summary>
    /// 生成变量声明
    /// </summary>
    private void GenerateVariables(StringBuilder sb, TestCase testCase)
    {
        sb.AppendLine("variables");
        sb.AppendLine("{");
        
        // 测试用例元信息
        sb.AppendLine("  // Test case metadata");
        sb.AppendLine($"  char gTestCaseName[100] = \"{testCase.Metadata.Name}\";");
        sb.AppendLine($"  char gTestDescription[200] = \"{testCase.Metadata.Description}\";");
        sb.AppendLine();

        // 测试配置
        sb.AppendLine("  // Test configuration");
        sb.AppendLine("  const dword DIAG_REQ_ID = 0x123;");
        sb.AppendLine("  const dword DIAG_RES_ID = 0x456;");
        sb.AppendLine($"  const dword BAUDRATE = {testCase.Environment.BaudRate};");
        sb.AppendLine();

        // 测试变量
        sb.AppendLine("  // Test variables");
        sb.AppendLine("  int gCurrentStep = 0;");
        sb.AppendLine("  int gTestResult = 1; // 1 = PASS, 0 = FAIL");
        sb.AppendLine("  char gTestLog[1000] = \"\";");
        sb.AppendLine();
        sb.AppendLine("  // Response handling variables");
        sb.AppendLine("  int responseReceived = 0;");
        sb.AppendLine("  message response;");
        sb.AppendLine("  int consecutiveFrameCount = 0;");
        sb.AppendLine();
        sb.AppendLine("  // Timers");
        sb.AppendLine("  timer responseTimer;");
        sb.AppendLine("  timer waitTimer;");
        sb.AppendLine("  timer cfTimer;");
        
        sb.AppendLine("}");
        sb.AppendLine();
    }

    /// <summary>
    /// 生成主测试函数
    /// </summary>
    private void GenerateMainTestFunction(StringBuilder sb, TestCase testCase)
    {
        var functionName = GenerateTestCaseFunctionName(testCase.Metadata.Name);
        
        sb.AppendLine("/*");
        sb.AppendLine($" * Test case: {testCase.Metadata.Name}");
        sb.AppendLine($" * Description: {testCase.Metadata.Description}");
        sb.AppendLine(" */");
        sb.AppendLine($"testcase {functionName}()");
        sb.AppendLine("{");
        sb.AppendLine("  // Initialize common library");
        sb.AppendLine("  CommonLibrary_Initialize();");
        sb.AppendLine();
        sb.AppendLine("  // Initialize test");
        sb.AppendLine($"  TestCaseStart(\"{testCase.Metadata.Name}\", \"{testCase.Metadata.Description}\");");
        sb.AppendLine();

        // 生成测试步骤调用
        for (int i = 0; i < testCase.TestSteps.Count; i++)
        {
            var step = testCase.TestSteps[i];
            var stepFunctionName = $"Step{step.StepNumber}_{GenerateStepFunctionName(step.Description)}";
            
            sb.AppendLine($"  // Step {step.StepNumber}: {step.Description}");
            sb.AppendLine($"  gCurrentStep = {step.StepNumber};");
            sb.AppendLine($"  if (!{stepFunctionName}())");
            sb.AppendLine("  {");
            sb.AppendLine($"    TestCaseFail(\"Step {step.StepNumber} failed: {step.Description}\");");
            sb.AppendLine("    return;");
            sb.AppendLine("  }");
            sb.AppendLine();
        }

        sb.AppendLine("  // Test completed successfully");
        sb.AppendLine("  TestCasePass(\"All test steps completed successfully\");");
        sb.AppendLine("}");
        sb.AppendLine();
    }

    /// <summary>
    /// 生成测试步骤函数
    /// </summary>
    private void GenerateTestStepFunctions(StringBuilder sb, TestCase testCase)
    {
        foreach (var step in testCase.TestSteps)
        {
            var stepFunctionName = $"Step{step.StepNumber}_{GenerateStepFunctionName(step.Description)}";
            
            sb.AppendLine("/*");
            sb.AppendLine($" * Step {step.StepNumber}: {step.Description}");
            sb.AppendLine(" */");
            sb.AppendLine($"int {stepFunctionName}()");
            sb.AppendLine("{");
            sb.AppendLine("  int result = 0;");
            sb.AppendLine();
            sb.AppendLine($"  TestStepStart({step.StepNumber}, \"{step.Description}\");");

        // 添加注释（如果有）
        if (step.Action != null && !string.IsNullOrEmpty(GetActionComment(step.Action)))
        {
            sb.AppendLine($"  // {GetActionComment(step.Action)}");
        }
            sb.AppendLine();

            // 根据动作类型生成不同的代码
            GenerateStepActionCode(sb, step);

            sb.AppendLine();
            sb.AppendLine("  return result;");
            sb.AppendLine("}");
            sb.AppendLine();
        }
    }

    /// <summary>
    /// 生成步骤动作代码
    /// </summary>
    private void GenerateStepActionCode(StringBuilder sb, TestStep step)
    {
        switch (step.Action)
        {
            case SendAndVerifyAction sendAction:
                GenerateSendAndVerifyCode(sb, step, sendAction);
                break;
            case WaitAction waitAction:
                GenerateWaitCode(sb, step, waitAction);
                break;
            case FlowControlSequenceAction flowAction:
                GenerateFlowControlCode(sb, step, flowAction);
                break;

            case RepeatFlowControlAction repeatAction:
                GenerateRepeatFlowControlCode(sb, step, repeatAction);
                break;

            case LoopFlowControlAction loopAction:
                GenerateLoopFlowControlCode(sb, step, loopAction);
                break;
            default:
                sb.AppendLine("  // TODO: Implement action logic");
                sb.AppendLine($"  TestStepPass({step.StepNumber}, \"Step completed\");");
                sb.AppendLine("  result = 1;");
                break;
        }
    }

    /// <summary>
    /// 生成发送和验证代码
    /// </summary>
    private void GenerateSendAndVerifyCode(StringBuilder sb, TestStep step, SendAndVerifyAction action)
    {
        sb.AppendLine("  message diagReq msg;");
        sb.AppendLine("  message diagRes response;");
        sb.AppendLine("  int responseReceived = 0;");
        sb.AppendLine();

        sb.AppendLine("  // Prepare diagnostic request");
        sb.AppendLine("  msg.id = DIAG_REQ_ID;");
        sb.AppendLine("  msg.dlc = 8;");

        // 解析发送数据
        if (!string.IsNullOrEmpty(action.SendData))
        {
            var sendBytes = ParseHexString(action.SendData);
            sb.AppendLine($"  // Send data: {action.SendData}");
            sb.AppendLine($"  msg.dlc = {Math.Min(sendBytes.Count, 8)};");
            for (int i = 0; i < sendBytes.Count && i < 8; i++)
            {
                sb.AppendLine($"  msg.byte({i}) = 0x{sendBytes[i]:X2};");
            }
            // 清零未使用的字节
            for (int i = sendBytes.Count; i < 8; i++)
            {
                sb.AppendLine($"  msg.byte({i}) = 0x00;");
            }
        }
        else
        {
            sb.AppendLine("  // Default diagnostic data");
            sb.AppendLine("  msg.byte(0) = 0x22;  // Read Data By Identifier");
            sb.AppendLine("  msg.byte(1) = 0xF1;  // DID high byte");
            sb.AppendLine("  msg.byte(2) = 0x86;  // DID low byte");
            for (int i = 3; i < 8; i++)
            {
                sb.AppendLine($"  msg.byte({i}) = 0x00;");
            }
        }

        sb.AppendLine();
        sb.AppendLine("  // Send request");
        sb.AppendLine("  output(msg);");
        sb.AppendLine();

        // 等待响应
        var timeoutMs = action.TimeoutMs > 0 ? action.TimeoutMs : 100;
        sb.AppendLine($"  // Wait for response (timeout: {timeoutMs}ms)");
        sb.AppendLine($"  setTimer(responseTimer, {timeoutMs});");
        sb.AppendLine("  responseReceived = 0;");
        sb.AppendLine();
        sb.AppendLine("  // Wait for diagnostic response");
        sb.AppendLine("  while (!responseReceived && testGetTimer(responseTimer) > 0)");
        sb.AppendLine("  {");
        sb.AppendLine("    // Response would be received via message handler");
        sb.AppendLine("    // on message DIAG_RES_ID { responseReceived = 1; response = this; }");
        sb.AppendLine("    testWaitForTimeout(10);  // Small delay");
        sb.AppendLine("  }");
        sb.AppendLine();

        // 验证响应
        if (!string.IsNullOrEmpty(action.ExpectedResponse))
        {
            var expectedBytes = ParseHexString(action.ExpectedResponse);
            sb.AppendLine($"  // Verify response: {action.ExpectedResponse}");
            sb.AppendLine("  if (responseReceived)");
            sb.AppendLine("  {");
            sb.AppendLine("    int responseValid = 1;");

            // 生成响应验证代码
            for (int i = 0; i < expectedBytes.Count && i < 8; i++)
            {
                sb.AppendLine($"    if (response.byte({i}) != 0x{expectedBytes[i]:X2})");
                sb.AppendLine("    {");
                sb.AppendLine($"      write(\"Response byte {i} mismatch: expected 0x{expectedBytes[i]:X2}, got 0x%02X\", response.byte({i}));");
                sb.AppendLine("      responseValid = 0;");
                sb.AppendLine("    }");
            }

            sb.AppendLine("    if (responseValid)");
            sb.AppendLine("    {");
            sb.AppendLine($"      TestStepPass({step.StepNumber}, \"Response received and verified\");");
            sb.AppendLine("      result = 1;");
            sb.AppendLine("    }");
            sb.AppendLine("    else");
            sb.AppendLine("    {");
            sb.AppendLine($"      TestStepFail({step.StepNumber}, \"Response verification failed\");");
            sb.AppendLine("      result = 0;");
            sb.AppendLine("    }");
            sb.AppendLine("  }");
            sb.AppendLine("  else");
            sb.AppendLine("  {");
            sb.AppendLine($"    TestStepFail({step.StepNumber}, \"No response received within timeout\");");
            sb.AppendLine("    result = 0;");
            sb.AppendLine("  }");
        }
        else
        {
            sb.AppendLine($"  TestStepPass({step.StepNumber}, \"Request sent successfully\");");
            sb.AppendLine("  result = 1;");
        }
    }

    /// <summary>
    /// 生成等待代码
    /// </summary>
    private void GenerateWaitCode(StringBuilder sb, TestStep step, WaitAction action)
    {
        var duration = action.DurationMs > 0 ? action.DurationMs : 1000;
        sb.AppendLine($"  // Wait for {duration}ms");
        sb.AppendLine($"  setTimer(waitTimer, {duration});");
        sb.AppendLine("  while (testGetTimer(waitTimer) > 0)");
        sb.AppendLine("  {");
        sb.AppendLine("    // Waiting...");
        sb.AppendLine("  }");
        sb.AppendLine($"  TestStepPass({step.StepNumber}, \"Wait completed ({duration}ms)\");");
        sb.AppendLine("  result = 1;");
    }

    /// <summary>
    /// 生成流控制代码
    /// </summary>
    private void GenerateFlowControlCode(StringBuilder sb, TestStep step, FlowControlSequenceAction action)
    {
        sb.AppendLine("  int result = 0;");
        sb.AppendLine();

        if (!string.IsNullOrEmpty(action.FlowControlFrame))
        {
            var fcBytes = ParseHexString(action.FlowControlFrame);
            var maxFrames = action.MaxFrames > 0 ? action.MaxFrames : (fcBytes.Count > 1 ? fcBytes[1] : 0);

            sb.AppendLine($"  // Use common flow control function");
            sb.AppendLine($"  result = DiagCommon_FlowControlSequence(0x{fcBytes[0]:X2}, 0x{fcBytes[1]:X2}, 0x{fcBytes[2]:X2}, {maxFrames}, \"{action.ExpectedConsecutiveFramePattern}\");");
            sb.AppendLine();
            sb.AppendLine("  if (result)");
            sb.AppendLine("  {");
            sb.AppendLine($"    TestStepPass({step.StepNumber}, \"Flow control sequence completed\");");
            sb.AppendLine("  }");
            sb.AppendLine("  else");
            sb.AppendLine("  {");
            sb.AppendLine($"    TestStepFail({step.StepNumber}, \"Flow control sequence failed\");");
            sb.AppendLine("  }");
        }
        else
        {
            sb.AppendLine($"  TestStepFail({step.StepNumber}, \"No flow control frame specified\");");
            sb.AppendLine("  result = 0;");
        }
    }

    /// <summary>
    /// 生成消息处理器
    /// </summary>
    private void GenerateMessageHandlers(StringBuilder sb, TestCase testCase)
    {
        sb.AppendLine("/*");
        sb.AppendLine(" * Message handlers");
        sb.AppendLine(" */");
        sb.AppendLine();

        // 诊断响应处理器
        sb.AppendLine("on message DIAG_RES_ID");
        sb.AppendLine("{");
        sb.AppendLine("  // Handle diagnostic response");
        sb.AppendLine("  responseReceived = 1;");
        sb.AppendLine("  response = this;");
        sb.AppendLine("  write(\"Received diagnostic response: ID=0x%03X, DLC=%d\", this.id, this.dlc);");
        sb.AppendLine("}");
        sb.AppendLine();

        // 连续帧处理器（用于多帧传输）
        sb.AppendLine("on message *");
        sb.AppendLine("{");
        sb.AppendLine("  // Handle consecutive frames for multi-frame transfers");
        sb.AppendLine("  if ((this.byte(0) & 0xF0) == 0x20)  // Consecutive frame");
        sb.AppendLine("  {");
        sb.AppendLine("    consecutiveFrameCount++;");
        sb.AppendLine("    write(\"Received consecutive frame #%d\", consecutiveFrameCount);");
        sb.AppendLine("  }");
        sb.AppendLine("}");
        sb.AppendLine();
    }

    /// <summary>
    /// 生成辅助函数
    /// </summary>
    private void GenerateHelperFunctions(StringBuilder sb, TestCase testCase)
    {
        sb.AppendLine("/*");
        sb.AppendLine(" * Helper functions");
        sb.AppendLine(" */");
        sb.AppendLine();
        
        // 测试框架函数声明
        sb.AppendLine("// Test framework functions (would be implemented in Common.can)");
        sb.AppendLine("void TestCaseStart(char* name, char* description) { }");
        sb.AppendLine("void TestCasePass(char* message) { }");
        sb.AppendLine("void TestCaseFail(char* message) { }");
        sb.AppendLine("void TestStepStart(int stepNumber, char* description) { }");
        sb.AppendLine("void TestStepPass(int stepNumber, char* message) { }");
        sb.AppendLine("void TestStepFail(int stepNumber, char* message) { }");
    }

    /// <summary>
    /// 生成测试用例函数名
    /// </summary>
    private string GenerateTestCaseFunctionName(string testCaseName)
    {
        // 清理函数名，只保留字母数字和下划线
        var cleanName = System.Text.RegularExpressions.Regex.Replace(testCaseName, @"[^a-zA-Z0-9_]", "_");
        return $"TC_{cleanName}";
    }

    /// <summary>
    /// 生成步骤函数名
    /// </summary>
    private string GenerateStepFunctionName(string stepDescription)
    {
        // 清理函数名，只保留字母数字和下划线，并限制长度
        var cleanName = System.Text.RegularExpressions.Regex.Replace(stepDescription, @"[^a-zA-Z0-9_]", "_");
        if (cleanName.Length > 30)
            cleanName = cleanName.Substring(0, 30);
        return cleanName;
    }

    /// <summary>
    /// 解析十六进制字符串为字节数组
    /// </summary>
    /// <param name="hexString">十六进制字符串，如"03 22 F1 86"</param>
    /// <returns>字节列表</returns>
    private List<byte> ParseHexString(string hexString)
    {
        var bytes = new List<byte>();
        if (string.IsNullOrEmpty(hexString))
            return bytes;

        // 移除空格和其他分隔符
        var cleanHex = Regex.Replace(hexString, @"[^0-9A-Fa-f]", "");

        // 每两个字符组成一个字节
        for (int i = 0; i < cleanHex.Length; i += 2)
        {
            if (i + 1 < cleanHex.Length)
            {
                var hexByte = cleanHex.Substring(i, 2);
                if (byte.TryParse(hexByte, System.Globalization.NumberStyles.HexNumber, null, out byte b))
                {
                    bytes.Add(b);
                }
            }
        }

        return bytes;
    }

    /// <summary>
    /// 解析期望响应模式，支持通配符
    /// </summary>
    private (List<byte> pattern, List<byte> mask) ParseExpectedResponse(string expectedResponse)
    {
        var pattern = new List<byte>();
        var mask = new List<byte>();

        var parts = expectedResponse.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        foreach (var part in parts)
        {
            if (part == "**")
            {
                // 完全忽略该字节
                pattern.Add(0x00);
                mask.Add(0x00);
            }
            else if (part.EndsWith("*"))
            {
                // 高4位匹配，低4位忽略
                var highNibble = part.Substring(0, 1);
                if (byte.TryParse(highNibble, NumberStyles.HexNumber, null, out var nibble))
                {
                    pattern.Add((byte)(nibble << 4));
                    mask.Add(0xF0);
                }
                else
                {
                    pattern.Add(0x00);
                    mask.Add(0x00);
                }
            }
            else if (part.StartsWith("*"))
            {
                // 低4位匹配，高4位忽略
                var lowNibble = part.Substring(1, 1);
                if (byte.TryParse(lowNibble, NumberStyles.HexNumber, null, out var nibble))
                {
                    pattern.Add(nibble);
                    mask.Add(0x0F);
                }
                else
                {
                    pattern.Add(0x00);
                    mask.Add(0x00);
                }
            }
            else
            {
                // 精确匹配
                if (byte.TryParse(part, NumberStyles.HexNumber, null, out var value))
                {
                    pattern.Add(value);
                    mask.Add(0xFF);
                }
                else
                {
                    pattern.Add(0x00);
                    mask.Add(0x00);
                }
            }
        }

        return (pattern, mask);
    }

    /// <summary>
    /// 获取动作的注释
    /// </summary>
    private string GetActionComment(TestAction action)
    {
        return action switch
        {
            SendAndVerifyAction sendAction => sendAction.Comment,
            FlowControlSequenceAction flowAction => flowAction.Comment,
            _ => string.Empty
        };
    }

    /// <summary>
    /// 生成重复流控制代码
    /// </summary>
    private void GenerateRepeatFlowControlCode(StringBuilder sb, TestStep step, RepeatFlowControlAction action)
    {
        sb.AppendLine("  int result = 0;");
        sb.AppendLine("  int iteration = 0;");
        sb.AppendLine("  int messageComplete = 0;");
        sb.AppendLine("  int stepResult = 0;");
        sb.AppendLine();

        if (!string.IsNullOrEmpty(action.FcFrame))
        {
            var fcBytes = ParseHexString(action.FcFrame);
            sb.AppendLine($"  // Repeat flow control until message is complete");
            sb.AppendLine($"  while (!messageComplete && iteration < {action.MaxIterations})");
            sb.AppendLine("  {");
            sb.AppendLine("    iteration++;");
            sb.AppendLine("    write(\"Flow control iteration %d\", iteration);");
            sb.AppendLine();
            sb.AppendLine("    // Execute flow control sequence");
            sb.AppendLine($"    stepResult = DiagCommon_FlowControlSequence(0x{fcBytes[0]:X2}, 0x{fcBytes[1]:X2}, 0x{fcBytes[2]:X2}, {fcBytes[1]}, \"2* ** ** ** ** ** ** **\");");
            sb.AppendLine();
            sb.AppendLine("    if (!stepResult)");
            sb.AppendLine("    {");
            sb.AppendLine($"      TestStepFail({step.StepNumber}, \"Flow control sequence failed in iteration %d\", iteration);");
            sb.AppendLine("      return 0;");
            sb.AppendLine("    }");
            sb.AppendLine();
            sb.AppendLine("    // Check if message is complete");
            sb.AppendLine("    messageComplete = DiagCommon_IsMessageComplete();");
            sb.AppendLine();
            sb.AppendLine("    if (messageComplete)");
            sb.AppendLine("    {");
            sb.AppendLine("      write(\"Message completed after %d iterations\", iteration);");
            sb.AppendLine("      break;");
            sb.AppendLine("    }");
            sb.AppendLine();
            sb.AppendLine("    // Small delay between iterations");
            sb.AppendLine("    testWaitForTimeout(50);");
            sb.AppendLine("  }");
            sb.AppendLine();
            sb.AppendLine("  if (messageComplete)");
            sb.AppendLine("  {");
            sb.AppendLine($"    TestStepPass({step.StepNumber}, \"Repeat flow control completed successfully after %d iterations\", iteration);");
            sb.AppendLine("    result = 1;");
            sb.AppendLine("  }");
            sb.AppendLine("  else");
            sb.AppendLine("  {");
            sb.AppendLine($"    TestStepFail({step.StepNumber}, \"Message not completed after %d iterations\", iteration);");
            sb.AppendLine("    result = 0;");
            sb.AppendLine("  }");
        }
    }

    /// <summary>
    /// 生成循环流控制代码
    /// </summary>
    private void GenerateLoopFlowControlCode(StringBuilder sb, TestStep step, LoopFlowControlAction action)
    {
        sb.AppendLine("  message fcFrame, reqFrame;");
        sb.AppendLine("  int bs, startBs, endBs;");
        sb.AppendLine();

        // 处理BS范围
        if (!string.IsNullOrEmpty(action.BsRange))
        {
            var parts = action.BsRange.Split('-');
            if (parts.Length == 2 && int.TryParse(parts[0], out var start) && int.TryParse(parts[1], out var end))
            {
                sb.AppendLine($"  startBs = {start};");
                sb.AppendLine($"  endBs = {end};");
            }
            else
            {
                sb.AppendLine("  startBs = 2;");
                sb.AppendLine("  endBs = 6;");
            }
        }
        else
        {
            sb.AppendLine("  startBs = 2;");
            sb.AppendLine("  endBs = 6;");
        }
        
        sb.AppendLine("  for (bs = startBs; bs <= endBs; bs++)");
        sb.AppendLine("  {");
        sb.AppendLine("    write(\"Testing BS = %d\", bs);");

        // 发送基础请求
        if (!string.IsNullOrEmpty(action.BaseRequest))
        {
            var reqBytes = ParseHexString(action.BaseRequest);
            sb.AppendLine("    // Send base request");
            sb.AppendLine("    reqFrame.id = DIAG_REQ_ID;");
            sb.AppendLine($"    reqFrame.dlc = {Math.Min(reqBytes.Count, 8)};");

            for (int i = 0; i < reqBytes.Count && i < 8; i++)
            {
                sb.AppendLine($"    reqFrame.byte({i}) = 0x{reqBytes[i]:X2};");
            }

            sb.AppendLine("    output(reqFrame);");
            sb.AppendLine("    testWaitForTimeout(50);");
        }

        // 发送流控制帧
        sb.AppendLine("    // Send flow control frame with current BS");
        sb.AppendLine("    fcFrame.id = DIAG_REQ_ID;");
        sb.AppendLine("    fcFrame.dlc = 8;");
        sb.AppendLine("    fcFrame.byte(0) = 0x30;");
        sb.AppendLine("    fcFrame.byte(1) = bs;");
        sb.AppendLine("    fcFrame.byte(2) = 0x00;");
        sb.AppendLine("    for (int i = 3; i < 8; i++) fcFrame.byte(i) = 0x00;");
        sb.AppendLine("    output(fcFrame);");
        sb.AppendLine();

        sb.AppendLine("    // Wait and verify consecutive frames");
        sb.AppendLine("    setTimer(cfTimer, 1000);");
        sb.AppendLine("    int cfReceived = 0;");
        sb.AppendLine("    while (testGetTimer(cfTimer) > 0 && cfReceived < bs)");
        sb.AppendLine("    {");
        sb.AppendLine("      // Check for consecutive frames");
        sb.AppendLine("      // cfReceived = CheckConsecutiveFrames(bs);");
        sb.AppendLine("    }");
        sb.AppendLine("    testWaitForTimeout(100);");
        sb.AppendLine("  }");

        sb.AppendLine($"  TestStepPass({step.StepNumber}, \"Loop flow control completed\");");
        sb.AppendLine("  result = 1;");
    }
}
