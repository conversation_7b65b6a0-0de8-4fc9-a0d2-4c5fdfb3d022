using System;
using System.Globalization;
using System.Threading;
using DSL2CAPL.UI.Services;
using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🔧 测试格式化修复...");
        
        // 设置不同的文化设置来测试格式化问题
        var cultures = new[] { "en-US", "de-DE", "fr-FR", "zh-CN" };
        
        foreach (var cultureName in cultures)
        {
            try
            {
                Console.WriteLine($"\n测试文化设置: {cultureName}");
                var culture = new CultureInfo(cultureName);
                Thread.CurrentThread.CurrentCulture = culture;
                Thread.CurrentThread.CurrentUICulture = culture;
                
                // 测试DSL内容
                var dslContent = @"
metadata:
  name: ""Test Case TC01 BlockSizeHandling""
  description: ""Block size handling physical addressing - Default session""
  author: ""DSL2CAPL Generator""
  version: ""1.0""
  generated_on: ""2025-08-04 16:53:40""
  generator: ""DSL2CAPL Converter v0.1.0""

environment:
  bus_type: ""CAN""
  ecu_type: ""Generic""
  addressing: ""Physical""
  baudrate: 500000

test_steps:
- step: 1
  description: ""重新完全初始化测试系统""
  action:
    type: ""send_and_verify""
    send: ""03 22 E0 00 00 00 00 00""
    expect: ""1* ** 62 ED ** ** ** **""
    timeout: 150
    comment: ""BS=1, only one CF shall be sent in the block""
";

                // 创建转换器
                var dslParser = new SimpleDslParser();
                var caplGenerator = new SimpleCaplGenerator();
                var converter = new BidirectionalConverter(dslParser, caplGenerator);
                
                // 执行转换
                var result = await converter.DslToCaplAsync(dslContent);
                
                if (result.IsSuccess)
                {
                    Console.WriteLine($"✓ 转换成功 - 文化设置: {cultureName}");
                    Console.WriteLine($"  质量评分: {result.QualityScore}");
                    Console.WriteLine($"  耗时: {result.ElapsedMs}ms");
                    Console.WriteLine($"  代码长度: {result.ConvertedContent.Length} 字符");
                }
                else
                {
                    Console.WriteLine($"❌ 转换失败 - 文化设置: {cultureName}");
                    foreach (var error in result.Errors)
                    {
                        Console.WriteLine($"  错误: {error}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 异常 - 文化设置: {cultureName}");
                Console.WriteLine($"  错误类型: {ex.GetType().Name}");
                Console.WriteLine($"  错误消息: {ex.Message}");
                
                if (ex.Message.Contains("Input string was not in a correct format"))
                {
                    Console.WriteLine("  ⚠️  发现格式化错误！");
                }
            }
        }
        
        Console.WriteLine("\n🎉 格式化测试完成！");
    }
}
