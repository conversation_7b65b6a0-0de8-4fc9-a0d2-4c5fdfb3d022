{"version": 3, "targets": {"net7.0-windows7.0": {"Microsoft.Data.Sqlite.Core/7.0.0": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "compile": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore/7.0.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.0", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"related": ".xml"}}}, "Microsoft.EntityFrameworkCore.Sqlite/7.0.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "7.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.2"}, "compile": {"lib/net6.0/_._": {}}, "runtime": {"lib/net6.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/7.0.0": {"type": "package", "dependencies": {"Microsoft.Data.Sqlite.Core": "7.0.0", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0"}, "compile": {"lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"related": ".xml"}}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.DependencyModel.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "compile": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "compile": {"lib/net7.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Scriban/5.9.0": {"type": "package", "compile": {"lib/net7.0/Scriban.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/Scriban.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"type": "package", "dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.2", "SQLitePCLRaw.provider.e_sqlite3": "2.1.2"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {}}}, "SQLitePCLRaw.core/2.1.2": {"type": "package", "dependencies": {"System.Memory": "4.5.3"}, "compile": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets": {}}, "runtimeTargets": {"runtimes/alpine-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "alpine-arm"}, "runtimes/alpine-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "alpine-arm64"}, "runtimes/alpine-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "alpine-x64"}, "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a": {"assetType": "native", "rid": "browser-wasm"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-armel"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-mips64"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-s390x"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"assetType": "native", "rid": "linux-x86"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-arm/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/e_sqlite3.dll": {"assetType": "native", "rid": "win-x86"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"type": "package", "dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "compile": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {}}}, "System.Memory/4.5.3": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/7.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "7.0.0"}, "compile": {"lib/net7.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "DSL2CAPL.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "compile": {"bin/placeholder/DSL2CAPL.Core.dll": {}}, "runtime": {"bin/placeholder/DSL2CAPL.Core.dll": {}}}, "DSL2CAPL.Data/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"DSL2CAPL.Core": "1.0.0", "Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.EntityFrameworkCore.Sqlite": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0"}, "compile": {"bin/placeholder/DSL2CAPL.Data.dll": {}}, "runtime": {"bin/placeholder/DSL2CAPL.Data.dll": {}}}, "DSL2CAPL.Generator/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"DSL2CAPL.Core": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Scriban": "5.9.0"}, "compile": {"bin/placeholder/DSL2CAPL.Generator.dll": {}}, "runtime": {"bin/placeholder/DSL2CAPL.Generator.dll": {}}}, "DSL2CAPL.Parser/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"DSL2CAPL.Core": "1.0.0"}, "compile": {"bin/placeholder/DSL2CAPL.Parser.dll": {}}, "runtime": {"bin/placeholder/DSL2CAPL.Parser.dll": {}}}, "DSL2CAPL.UI/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "dependencies": {"DSL2CAPL.Core": "1.0.0", "DSL2CAPL.Data": "1.0.0", "DSL2CAPL.Generator": "1.0.0", "DSL2CAPL.Parser": "1.0.0"}, "compile": {"bin/placeholder/DSL2CAPL.UI.dll": {}}, "runtime": {"bin/placeholder/DSL2CAPL.UI.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App"]}}}, "libraries": {"Microsoft.Data.Sqlite.Core/7.0.0": {"sha512": "WC7SANtaFTmQ/WPyhrE96h88MrH28C7kTBenDf5Eo+IR6CbWM0Uw2pR2++tyYr3qe/zSIsIYroEupB1mLg/adw==", "type": "package", "path": "microsoft.data.sqlite.core/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.Data.Sqlite.dll", "lib/net6.0/Microsoft.Data.Sqlite.xml", "lib/netstandard2.0/Microsoft.Data.Sqlite.dll", "lib/netstandard2.0/Microsoft.Data.Sqlite.xml", "microsoft.data.sqlite.core.7.0.0.nupkg.sha512", "microsoft.data.sqlite.core.nuspec"]}, "Microsoft.EntityFrameworkCore/7.0.0": {"sha512": "9W+IfmAzMrp2ZpKZLhgTlWljSBM9Erldis1us61DAGi+L7Q6vilTbe1G2zDxtYO8F2H0I0Qnupdx5Cp4s2xoZw==", "type": "package", "path": "microsoft.entityframeworkcore/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "buildTransitive/net6.0/Microsoft.EntityFrameworkCore.props", "lib/net6.0/Microsoft.EntityFrameworkCore.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.7.0.0.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"sha512": "Pfu3Zjj5+d2Gt27oE9dpGiF/VobBB+s5ogrfI9sBsXQE1SG49RqVz5+IyeNnzhyejFrPIQsPDRMchhcojy4Hbw==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.7.0.0.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {"sha512": "Qkd2H+jLe37o5ku+LjT6qf7kAHY75Yfn2bBDQgqr13DTOLYpEy1Mt93KPFjaZvIu/srEcbfGGMRL7urKm5zN8Q==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.7.0.0.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"sha512": "eQiYygtR2xZ0Uy7KtiFRHpoEx/U8xNwbNRgu1pEJgSxbJLtg6tDL1y2YcIbSuIRSNEljXIIHq/apEhGm1QL70g==", "type": "package", "path": "microsoft.entityframeworkcore.relational/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite/7.0.0": {"sha512": "tufYoEHetVeALPRqzCi4YukL+uoS0pBi/6m3uSCuM5NxO/tTbuizvvBRa3qJNYZOvF3K7m4lUmmcCymKr3JSSQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/_._", "microsoft.entityframeworkcore.sqlite.7.0.0.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.nuspec"]}, "Microsoft.EntityFrameworkCore.Sqlite.Core/7.0.0": {"sha512": "aUClrz1PT06fPDY+9f2IeDhYXj3/oPxM0r3I6syiyP3Th59hObVI0QsRu5+y7FbJIkO3NgyAi+e2vzWGhmBVbQ==", "type": "package", "path": "microsoft.entityframeworkcore.sqlite.core/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll", "lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.xml", "microsoft.entityframeworkcore.sqlite.core.7.0.0.nupkg.sha512", "microsoft.entityframeworkcore.sqlite.core.nuspec"]}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"sha512": "IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"sha512": "xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "type": "package", "path": "microsoft.extensions.caching.memory/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net6.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net6.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net7.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net7.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"sha512": "f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"sha512": "elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "type": "package", "path": "microsoft.extensions.dependencyinjection/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"sha512": "h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyModel/7.0.0": {"sha512": "oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "type": "package", "path": "microsoft.extensions.dependencymodel/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyModel.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyModel.targets", "lib/net462/Microsoft.Extensions.DependencyModel.dll", "lib/net462/Microsoft.Extensions.DependencyModel.xml", "lib/net6.0/Microsoft.Extensions.DependencyModel.dll", "lib/net6.0/Microsoft.Extensions.DependencyModel.xml", "lib/net7.0/Microsoft.Extensions.DependencyModel.dll", "lib/net7.0/Microsoft.Extensions.DependencyModel.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyModel.xml", "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512", "microsoft.extensions.dependencymodel.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/7.0.0": {"sha512": "Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "type": "package", "path": "microsoft.extensions.logging/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.7.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"sha512": "kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/7.0.0": {"sha512": "lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "type": "package", "path": "microsoft.extensions.options/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.7.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/7.0.0": {"sha512": "um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "type": "package", "path": "microsoft.extensions.primitives/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.7.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Scriban/5.9.0": {"sha512": "V725wLd3YKTRua8kTHUwJYTvIouNqyMBzNaNamCLlZPTyeP4gl+WJtqXDnWMe+mTftTGEVYZgbR1pLRmdKjK1g==", "type": "package", "path": "scriban/5.9.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Scriban.props", "build/Scriban.targets", "lib/net7.0/Scriban.dll", "lib/net7.0/Scriban.xml", "lib/netstandard2.0/Scriban.dll", "lib/netstandard2.0/Scriban.xml", "readme.md", "scriban.5.9.0.nupkg.sha512", "scriban.nuspec", "scriban.png", "src/Scriban/Functions/ArrayFunctions.cs", "src/Scriban/Functions/BuiltinFunctions.cs", "src/Scriban/Functions/DateTimeFunctions.cs", "src/Scriban/Functions/HtmlFunctions.cs", "src/Scriban/Functions/IncludeFunction.cs", "src/Scriban/Functions/LiquidBuiltinsFunctions.cs", "src/Scriban/Functions/MathFunctions.cs", "src/Scriban/Functions/ObjectFunctions.cs", "src/Scriban/Functions/RegexFunctions.cs", "src/Scriban/Functions/StringFunctions.cs", "src/Scriban/Functions/TimeSpanFunctions.cs", "src/Scriban/Helpers/BoxHelper.cs", "src/Scriban/Helpers/CharHelper.cs", "src/Scriban/Helpers/FastStack.cs", "src/Scriban/Helpers/InlineList.cs", "src/Scriban/Helpers/ReflectionHelper.cs", "src/Scriban/Helpers/StringHelper.cs", "src/Scriban/Helpers/ThrowHelper.cs", "src/Scriban/LogMessageBag.cs", "src/Scriban/Parsing/Lexer.cs", "src/Scriban/Parsing/LexerOptions.cs", "src/Scriban/Parsing/LogMessage.cs", "src/Scriban/Parsing/Parser.Expressions.cs", "src/Scriban/Parsing/Parser.Statements.Liquid.cs", "src/Scriban/Parsing/Parser.Statements.Scriban.cs", "src/Scriban/Parsing/Parser.Statements.cs", "src/Scriban/Parsing/Parser.Terminals.cs", "src/Scriban/Parsing/Parser.cs", "src/Scriban/Parsing/ParserOptions.cs", "src/Scriban/Parsing/ScriptLang.cs", "src/Scriban/Parsing/ScriptMode.cs", "src/Scriban/Parsing/SourceSpan.cs", "src/Scriban/Parsing/TextPosition.cs", "src/Scriban/Parsing/Token.cs", "src/Scriban/Parsing/TokenTextAttribute.cs", "src/Scriban/Parsing/TokenType.cs", "src/Scriban/Parsing/TokenTypeExtensions.cs", "src/Scriban/Parsing/Util.cs", "src/Scriban/Runtime/Accessors/ArrayAccessor.cs", "src/Scriban/Runtime/Accessors/DictionaryAccessor.cs", "src/Scriban/Runtime/Accessors/ListAccessor.cs", "src/Scriban/Runtime/Accessors/NullAccessor.cs", "src/Scriban/Runtime/Accessors/PrimitiveAccessor.cs", "src/Scriban/Runtime/Accessors/ScriptObjectAccessor.cs", "src/Scriban/Runtime/Accessors/StringAccessor.cs", "src/Scriban/Runtime/Accessors/TypedObjectAccessor.cs", "src/Scriban/Runtime/CustomFunction.Generated.cs", "src/Scriban/Runtime/DelegateCustomFunction.cs", "src/Scriban/Runtime/DynamicCustomFunction.cs", "src/Scriban/Runtime/EmptyScriptObject.cs", "src/Scriban/Runtime/IListAccessor.cs", "src/Scriban/Runtime/IObjectAccessor.cs", "src/Scriban/Runtime/IScriptCustomFunction.cs", "src/Scriban/Runtime/IScriptObject.cs", "src/Scriban/Runtime/IScriptOutput.cs", "src/Scriban/Runtime/IScriptTransformable.cs", "src/Scriban/Runtime/ITemplateLoader.cs", "src/Scriban/Runtime/MemberFilterDelegate.cs", "src/Scriban/Runtime/MemberRenamerDelegate.cs", "src/Scriban/Runtime/ScriptArray.cs", "src/Scriban/Runtime/ScriptLazy.cs", "src/Scriban/Runtime/ScriptMemberIgnoreAttribute.cs", "src/Scriban/Runtime/ScriptMemberImportFlags.cs", "src/Scriban/Runtime/ScriptObject.cs", "src/Scriban/Runtime/ScriptObjectExtensions.cs", "src/Scriban/Runtime/ScriptPipeArguments.cs", "src/Scriban/Runtime/ScriptRange.cs", "src/Scriban/Runtime/StandardMemberRenamer.cs", "src/Scriban/Runtime/StringBuilderOutput.cs", "src/Scriban/Runtime/TextWriterOutput.cs", "src/Scriban/ScribanAsync.generated.cs", "src/Scriban/ScribanVisitors.generated.cs", "src/Scriban/ScriptPrinter.cs", "src/Scriban/ScriptPrinterOptions.cs", "src/Scriban/Syntax/Expressions/IScriptTerminal.cs", "src/Scriban/Syntax/Expressions/ScriptAnonymousFunction.cs", "src/Scriban/Syntax/Expressions/ScriptArgumentBinary.cs", "src/Scriban/Syntax/Expressions/ScriptArrayInitializerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptAssignExpression.cs", "src/Scriban/Syntax/Expressions/ScriptBinaryExpression.cs", "src/Scriban/Syntax/Expressions/ScriptBinaryOperator.cs", "src/Scriban/Syntax/Expressions/ScriptConditionalExpression.cs", "src/Scriban/Syntax/Expressions/ScriptExpression.cs", "src/Scriban/Syntax/Expressions/ScriptFunctionCall.cs", "src/Scriban/Syntax/Expressions/ScriptIncrementDecrementExpression.cs", "src/Scriban/Syntax/Expressions/ScriptIndexerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptInterpolatedExpression.cs", "src/Scriban/Syntax/Expressions/ScriptIsEmptyExpression.cs", "src/Scriban/Syntax/Expressions/ScriptLiteral.cs", "src/Scriban/Syntax/Expressions/ScriptMemberExpression.cs", "src/Scriban/Syntax/Expressions/ScriptNamedArgument.cs", "src/Scriban/Syntax/Expressions/ScriptNestedExpression.cs", "src/Scriban/Syntax/Expressions/ScriptObjectInitializerExpression.cs", "src/Scriban/Syntax/Expressions/ScriptObjectMember.cs", "src/Scriban/Syntax/Expressions/ScriptPipeCall.cs", "src/Scriban/Syntax/Expressions/ScriptThisExpression.cs", "src/Scriban/Syntax/Expressions/ScriptUnaryExpression.cs", "src/Scriban/Syntax/Expressions/ScriptUnaryOperator.cs", "src/Scriban/Syntax/Expressions/ScriptVariable.cs", "src/Scriban/Syntax/Expressions/ScriptVariableScope.cs", "src/Scriban/Syntax/IScriptConvertibleFrom.cs", "src/Scriban/Syntax/IScriptConvertibleTo.cs", "src/Scriban/Syntax/IScriptCustomBinaryOperation.cs", "src/Scriban/Syntax/IScriptCustomImplicitMultiplyPrecedence.cs", "src/Scriban/Syntax/IScriptCustomType.cs", "src/Scriban/Syntax/IScriptCustomTypeInfo.cs", "src/Scriban/Syntax/IScriptCustomUnaryOperation.cs", "src/Scriban/Syntax/IScriptNamedArgumentContainer.cs", "src/Scriban/Syntax/IScriptVariablePath.cs", "src/Scriban/Syntax/IScriptVisitorContext.cs", "src/Scriban/Syntax/ScientificFunctionCallRewriter.cs", "src/Scriban/Syntax/ScriptArgumentException.cs", "src/Scriban/Syntax/ScriptCloner.cs", "src/<PERSON>riban/Syntax/ScriptFormatter.cs", "src/<PERSON>riban/Syntax/ScriptFormatterExtensions.cs", "src/Scriban/Syntax/ScriptFormatterFlags.cs", "src/Scriban/Syntax/ScriptFormatterOptions.cs", "src/Scriban/Syntax/ScriptFrontMatter.cs", "src/Scriban/Syntax/ScriptIdentifier.cs", "src/Scriban/Syntax/ScriptKeyword.cs", "src/Scriban/Syntax/ScriptList.cs", "src/Scriban/Syntax/ScriptNode.cs", "src/Scriban/Syntax/ScriptNodeExtensions.cs", "src/Scriban/Syntax/ScriptPage.cs", "src/Scriban/Syntax/ScriptParameterContainerExtensions.cs", "src/Scriban/Syntax/ScriptRewriter.cs", "src/Scriban/Syntax/ScriptRuntimeException.cs", "src/Scriban/Syntax/ScriptStringSlice.cs", "src/Scriban/Syntax/ScriptSyntaxAttribute.cs", "src/Scriban/Syntax/ScriptToken.cs", "src/Scriban/Syntax/ScriptTrivia.cs", "src/Scriban/Syntax/ScriptTriviaType.cs", "src/Scriban/Syntax/ScriptTriviaTypeExtensions.cs", "src/Scriban/Syntax/ScriptTrivias.cs", "src/Scriban/Syntax/ScriptVerbatim.cs", "src/Scriban/Syntax/ScriptVisitor.cs", "src/Scriban/Syntax/Statements/ScriptBlockStatement.cs", "src/Scriban/Syntax/Statements/ScriptBreakStatement.cs", "src/Scriban/Syntax/Statements/ScriptCaptureStatement.cs", "src/Scriban/Syntax/Statements/ScriptCaseStatement.cs", "src/Scriban/Syntax/Statements/ScriptConditionStatement.cs", "src/Scriban/Syntax/Statements/ScriptContinueStatement.cs", "src/Scriban/Syntax/Statements/ScriptElseStatement.cs", "src/Scriban/Syntax/Statements/ScriptEndStatement.cs", "src/Scriban/Syntax/Statements/ScriptEscapeStatement.cs", "src/Scriban/Syntax/Statements/ScriptExpressionStatement.cs", "src/Scriban/Syntax/Statements/ScriptFlowState.cs", "src/Scriban/Syntax/Statements/ScriptForStatement.cs", "src/Scriban/Syntax/Statements/ScriptFunction.cs", "src/Scriban/Syntax/Statements/ScriptIfStatement.cs", "src/Scriban/Syntax/Statements/ScriptImportStatement.cs", "src/Scriban/Syntax/Statements/ScriptLoopStatementBase.cs", "src/Scriban/Syntax/Statements/ScriptNopStatement.cs", "src/Scriban/Syntax/Statements/ScriptParameter.cs", "src/Scriban/Syntax/Statements/ScriptRawStatement.cs", "src/Scriban/Syntax/Statements/ScriptReadOnlyStatement.cs", "src/Scriban/Syntax/Statements/ScriptReturnStatement.cs", "src/Scriban/Syntax/Statements/ScriptStatement.cs", "src/Scriban/Syntax/Statements/ScriptTableRowStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhenStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhileStatement.cs", "src/Scriban/Syntax/Statements/ScriptWhitespaceMode.cs", "src/Scriban/Syntax/Statements/ScriptWithStatement.cs", "src/Scriban/Syntax/Statements/ScriptWrapStatement.cs", "src/Scriban/Template.cs", "src/Scriban/TemplateContext.Helpers.cs", "src/Scriban/TemplateContext.Variables.cs", "src/Scriban/TemplateContext.cs"]}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"sha512": "ilkvNhrTersLmIVAcDwwPqfhUFCg19Z1GVMvCSi3xk6Akq94f4qadLORQCq/T8+9JgMiPs+F/NECw5uauviaNw==", "type": "package", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/monoandroid90/SQLitePCLRaw.batteries_v2.dll", "lib/net461/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-android31.0/SQLitePCLRaw.batteries_v2.xml", "lib/net6.0-ios14.0/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-ios14.2/SQLitePCLRaw.batteries_v2.dll", "lib/net6.0-tvos10.0/SQLitePCLRaw.batteries_v2.dll", "lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll", "lib/xamarinios10/SQLitePCLRaw.batteries_v2.dll", "sqlitepclraw.bundle_e_sqlite3.2.1.2.nupkg.sha512", "sqlitepclraw.bundle_e_sqlite3.nuspec"]}, "SQLitePCLRaw.core/2.1.2": {"sha512": "A8EBepVqY2lnAp3a8jnhbgzF2tlj2S3HcJQGANTYg/TbYbKa8Z5cM1h74An/vy0svhfzT7tVY0sFmUglLgv+2g==", "type": "package", "path": "sqlitepclraw.core/2.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.0/SQLitePCLRaw.core.dll", "sqlitepclraw.core.2.1.2.nupkg.sha512", "sqlitepclraw.core.nuspec"]}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"sha512": "zibGtku8M4Eea1R3ZCAxc86QbNvyEN17mAcQkvWKBuHvRpMiK2g5anG4R5Be7cWKSd1i6baYz8y4dMMAKcXKPg==", "type": "package", "path": "sqlitepclraw.lib.e_sqlite3/2.1.2", "files": [".nupkg.metadata", ".signature.p7s", "buildTransitive/net461/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net6.0/SQLitePCLRaw.lib.e_sqlite3.targets", "buildTransitive/net7.0/SQLitePCLRaw.lib.e_sqlite3.targets", "lib/net461/_._", "lib/netstandard2.0/_._", "runtimes/alpine-arm/native/libe_sqlite3.so", "runtimes/alpine-arm64/native/libe_sqlite3.so", "runtimes/alpine-x64/native/libe_sqlite3.so", "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a", "runtimes/browser-wasm/nativeassets/net7.0/e_sqlite3.a", "runtimes/linux-arm/native/libe_sqlite3.so", "runtimes/linux-arm64/native/libe_sqlite3.so", "runtimes/linux-armel/native/libe_sqlite3.so", "runtimes/linux-mips64/native/libe_sqlite3.so", "runtimes/linux-musl-arm/native/libe_sqlite3.so", "runtimes/linux-musl-arm64/native/libe_sqlite3.so", "runtimes/linux-musl-x64/native/libe_sqlite3.so", "runtimes/linux-s390x/native/libe_sqlite3.so", "runtimes/linux-x64/native/libe_sqlite3.so", "runtimes/linux-x86/native/libe_sqlite3.so", "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib", "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib", "runtimes/osx-arm64/native/libe_sqlite3.dylib", "runtimes/osx-x64/native/libe_sqlite3.dylib", "runtimes/win-arm/native/e_sqlite3.dll", "runtimes/win-arm64/native/e_sqlite3.dll", "runtimes/win-x64/native/e_sqlite3.dll", "runtimes/win-x86/native/e_sqlite3.dll", "runtimes/win10-arm/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-arm64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x64/nativeassets/uap10.0/e_sqlite3.dll", "runtimes/win10-x86/nativeassets/uap10.0/e_sqlite3.dll", "sqlitepclraw.lib.e_sqlite3.2.1.2.nupkg.sha512", "sqlitepclraw.lib.e_sqlite3.nuspec"]}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"sha512": "lxCZarZdvAsMl2zw9bXHrXK6RxVhB4b23iTFhCOdHFhxfbsxLxWf+ocvswJwR/9Wh/E//ddMi+wJGqUKV7VwoA==", "type": "package", "path": "sqlitepclraw.provider.e_sqlite3/2.1.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll", "lib/netstandard2.0/SQLitePCLRaw.provider.e_sqlite3.dll", "sqlitepclraw.provider.e_sqlite3.2.1.2.nupkg.sha512", "sqlitepclraw.provider.e_sqlite3.nuspec"]}, "System.Memory/4.5.3": {"sha512": "3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "type": "package", "path": "system.memory/4.5.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.3.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encodings.Web/7.0.0": {"sha512": "OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "type": "package", "path": "system.text.encodings.web/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.7.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/7.0.0": {"sha512": "DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "type": "package", "path": "system.text.json/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "README.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.7.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "DSL2CAPL.Core/1.0.0": {"type": "project", "path": "../src/DSL2CAPL.Core/DSL2CAPL.Core.csproj", "msbuildProject": "../src/DSL2CAPL.Core/DSL2CAPL.Core.csproj"}, "DSL2CAPL.Data/1.0.0": {"type": "project", "path": "../src/DSL2CAPL.Data/DSL2CAPL.Data.csproj", "msbuildProject": "../src/DSL2CAPL.Data/DSL2CAPL.Data.csproj"}, "DSL2CAPL.Generator/1.0.0": {"type": "project", "path": "../src/DSL2CAPL.Generator/DSL2CAPL.Generator.csproj", "msbuildProject": "../src/DSL2CAPL.Generator/DSL2CAPL.Generator.csproj"}, "DSL2CAPL.Parser/1.0.0": {"type": "project", "path": "../src/DSL2CAPL.Parser/DSL2CAPL.Parser.csproj", "msbuildProject": "../src/DSL2CAPL.Parser/DSL2CAPL.Parser.csproj"}, "DSL2CAPL.UI/1.0.0": {"type": "project", "path": "../src/DSL2CAPL.UI/DSL2CAPL.UI.csproj", "msbuildProject": "../src/DSL2CAPL.UI/DSL2CAPL.UI.csproj"}}, "projectFileDependencyGroups": {"net7.0-windows7.0": ["DSL2CAPL.Core >= 1.0.0", "DSL2CAPL.Generator >= 1.0.0", "DSL2CAPL.Parser >= 1.0.0", "DSL2CAPL.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\BlockSizeTest\\BlockSizeTest.csproj", "projectName": "BlockSizeTest", "projectPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\BlockSizeTest\\BlockSizeTest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\BlockSizeTest\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {"D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj": {"projectPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj"}, "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj": {"projectPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj"}, "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj": {"projectPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj"}, "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.UI\\DSL2CAPL.UI.csproj": {"projectPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.UI\\DSL2CAPL.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.101\\RuntimeIdentifierGraph.json"}}}}