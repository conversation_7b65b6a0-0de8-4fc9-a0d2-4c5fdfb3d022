<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <AssemblyTitle>DSL2CAPL Parser Library</AssemblyTitle>
    <AssemblyDescription>DSL and CAPL parsing library for DSL2CAPL converter</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <!-- 暂时移除外部依赖，使用简化实现 -->
  <!--
  <ItemGroup>
    <PackageReference Include="Antlr4.Runtime.Standard" Version="4.13.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="7.0.0" />
    <PackageReference Include="YamlDotNet" Version="13.7.1" />
  </ItemGroup>
  -->

  <ItemGroup>
    <ProjectReference Include="..\DSL2CAPL.Core\DSL2CAPL.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Grammar\" />
    <Folder Include="DSL\" />
    <Folder Include="CAPL\" />
  </ItemGroup>

</Project>
