# Block Size Handling Test Case - Editor Test
metadata:
  name: "TG01_TC01_BlockSizeHandling"
  description: "Block size handling test for editor"
  author: "Test User"
  version: "1.0"

environment:
  bus_type: "CAN"
  ecu_type: "APP"
  addressing: "PHYSICAL"
  baudrate: 500000

test_steps:
  - step: 1
    description: "确认ECU Session状态"
    action:
      type: "send_and_verify"
      send: "03 22 F1 86 00 00 00 00"
      expect: "04 62 F1 86 ** 00 00 00"
      timeout: 100
      comment: "测试ECU响应"

  - step: 2
    description: "发送多帧数据请求"
    action:
      type: "send_and_verify"
      send: "03 22 ED 20 00 00 00 00"
      expect: "1* ** 62 ED 20 ** ** **"
      timeout: 150
      comment: "请求多帧数据"

  - step: 3
    description: "流控制测试"
    action:
      type: "flow_control_sequence"
      fc_frame: "30 00 00 00 00 00 00 00"
      expect_cf_pattern: "2* ** ** ** ** ** ** **"
      max_frames: 15
      comment: "BS=0测试"
