<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DSL2CAPL.Parser</name>
    </assembly>
    <members>
        <member name="T:DSL2CAPL.Parser.DSL.SimpleDslParser">
            <summary>
            简化的DSL解析器实现
            支持基本的YAML格式解析
            </summary>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.ParseAsync(System.String)">
            <summary>
            解析DSL文本为测试用例对象
            </summary>
            <param name="dslContent">DSL文本内容</param>
            <returns>解析结果</returns>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.ValidateSyntaxAsync(System.String)">
            <summary>
            验证DSL语法
            </summary>
            <param name="dslContent">DSL文本内容</param>
            <returns>验证结果</returns>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.ParseDslContent(System.String)">
            <summary>
            解析DSL内容为测试用例
            </summary>
            <param name="dslContent">DSL内容</param>
            <returns>测试用例对象</returns>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.ProcessSection(DSL2CAPL.Core.Models.TestCase,System.String,System.Collections.Generic.List{System.String})">
            <summary>
            处理DSL节内容
            </summary>
            <param name="testCase">测试用例对象</param>
            <param name="sectionName">节名称</param>
            <param name="sectionContent">节内容</param>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.ProcessMetadataSection(DSL2CAPL.Core.Models.TestCase,System.Collections.Generic.List{System.String})">
            <summary>
            处理元数据节
            </summary>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.ProcessEnvironmentSection(DSL2CAPL.Core.Models.TestCase,System.Collections.Generic.List{System.String})">
            <summary>
            处理环境节
            </summary>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.ProcessDataDefinitionsSection(DSL2CAPL.Core.Models.TestCase,System.Collections.Generic.List{System.String})">
            <summary>
            处理数据定义节
            </summary>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.ProcessTestStepsSection(DSL2CAPL.Core.Models.TestCase,System.Collections.Generic.List{System.String})">
            <summary>
            处理测试步骤节
            </summary>
        </member>
        <member name="M:DSL2CAPL.Parser.DSL.SimpleDslParser.CreateActionFromData(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            根据动作类型和数据创建动作对象
            </summary>
        </member>
    </members>
</doc>
