using System;
using System.IO;
using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;
using DSL2CAPL.UI.Services;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🔧 测试 block_size_test.dsl 文件...");
        
        try
        {
            // 读取DSL文件
            var dslFilePath = "../examples/block_size_test.dsl";
            if (!File.Exists(dslFilePath))
            {
                Console.WriteLine($"❌ DSL文件不存在: {dslFilePath}");
                return;
            }
            
            var dslContent = File.ReadAllText(dslFilePath);
            Console.WriteLine($"✓ 成功读取DSL文件，长度: {dslContent.Length} 字符");
            
            // 创建转换器
            var dslParser = new SimpleDslParser();
            var caplGenerator = new SimpleCaplGenerator();
            var converter = new BidirectionalConverter(dslParser, caplGenerator);
            
            // 执行转换
            Console.WriteLine("\n开始转换...");
            var result = await converter.DslToCaplAsync(dslContent);
            
            if (result.IsSuccess)
            {
                Console.WriteLine("✓ 转换成功！");
                Console.WriteLine($"  质量评分: {result.QualityScore}/100");
                Console.WriteLine($"  耗时: {result.ElapsedMs}ms");
                Console.WriteLine($"  生成代码长度: {result.ConvertedContent.Length} 字符");
                
                // 保存生成的CAPL代码
                var outputPath = "block_size_test_output.can";
                File.WriteAllText(outputPath, result.ConvertedContent);
                Console.WriteLine($"✓ CAPL代码已保存到: {outputPath}");
                
                // 显示生成代码的前几行
                var lines = result.ConvertedContent.Split('\n');
                Console.WriteLine("\n生成的CAPL代码预览（前10行）:");
                for (int i = 0; i < Math.Min(10, lines.Length); i++)
                {
                    Console.WriteLine($"  {i+1:D2}: {lines[i]}");
                }
                if (lines.Length > 10)
                {
                    Console.WriteLine($"  ... (还有 {lines.Length - 10} 行)");
                }
            }
            else
            {
                Console.WriteLine("❌ 转换失败！");
                Console.WriteLine("\n错误详情:");
                foreach (var error in result.Errors)
                {
                    Console.WriteLine($"  - {error}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 测试过程中发生异常:");
            Console.WriteLine($"  错误类型: {ex.GetType().Name}");
            Console.WriteLine($"  错误消息: {ex.Message}");
            if (ex.InnerException != null)
            {
                Console.WriteLine($"  内部错误: {ex.InnerException.Message}");
            }
        }
        
        Console.WriteLine("\n🎉 测试完成！");
    }
}
