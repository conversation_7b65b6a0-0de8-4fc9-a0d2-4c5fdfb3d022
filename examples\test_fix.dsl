# 测试修复的DSL示例
# 用于验证"Input string was not in a correct format"错误是否已修复

metadata:
  name: "TestFix_Example"
  description: "测试修复的简单示例"
  author: "DSL2CAPL Generator"
  version: "1.0"
  test_id: "TEST_001"

environment:
  bus_type: "CAN"
  ecu_type: "APP"
  addressing: "PHYSICAL"
  baudrate: 500000
  session: "DEFAULT"

test_steps:
  - step: 1
    description: "发送诊断请求"
    action:
      type: "send_and_verify"
      send: "03 22 F1 86 00 00 00 00"
      expect: "04 62 F1 86 ** 00 00 00"
      timeout: 100
      comment: "测试基本发送和验证"

  - step: 2
    description: "等待处理"
    action:
      type: "wait"
      duration: 500

  - step: 3
    description: "测试流控制"
    action:
      type: "flow_control_sequence"
      fc_frame: "30 01 00 00 00 00 00 00"
      expect_cf_pattern: "2* ** ** ** ** ** ** **"
      max_frames: 5
      comment: "测试流控制序列"

  - step: 4
    description: "测试循环流控制"
    action:
      type: "loop_flow_control"
      bs_range: "2-4"
      fc_frame_template: "30 {BS} 00 00 00 00 00 00"
      base_request: "03 22 ED 20 00 00 00 00" 