# DSL2CAPL Converter

一个将DSL（领域特定语言）转换为CAPL代码的工具，专门用于汽车CAN/CANFD诊断测试用例的快速生成。

## 项目概述

DSL2CAPL Converter旨在通过编写简洁的DSL代码，快速生成可运行的CAPL测试代码，从而提升测试用例编写效率、可靠性和可读性。同时集成AI功能，可以自动识别测试用例文档并转换为DSL。

## 主要特性

- 🚀 **高效转换**: DSL到CAPL的快速转换，减少80%的手工编码时间
- 🔄 **双向转换**: 支持DSL↔CAPL双向转换和预览
- 🤖 **AI辅助**: 自动识别测试用例文档并生成DSL
- ✅ **质量保证**: 多层验证机制确保生成代码的准确性
- 🎨 **友好界面**: 基于WPF的现代化用户界面
- 📝 **语法高亮**: 支持DSL和CAPL的语法高亮和自动补全
- 🔧 **可扩展**: 模块化设计，易于扩展和维护

## 技术架构

### 项目结构
```
DSL2CAPL/
├── src/
│   ├── DSL2CAPL.Core/          # 核心领域模型和接口
│   ├── DSL2CAPL.UI/            # WPF用户界面
│   ├── DSL2CAPL.Parser/        # DSL和CAPL解析器
│   ├── DSL2CAPL.Generator/     # 代码生成器
│   └── DSL2CAPL.Data/          # 数据访问层
├── tests/
│   └── DSL2CAPL.Tests/         # 单元测试和集成测试
├── Document/
│   ├── Design.md               # 详细技术方案
│   └── Task.md                 # 开发计划和进度
└── README.md
```

### 技术栈
- **框架**: .NET 8, WPF
- **UI**: Material Design, AvalonEdit
- **解析**: ANTLR4, YamlDotNet
- **模板**: Scriban
- **数据库**: SQLite, Entity Framework Core
- **测试**: xUnit, Moq, FluentAssertions
- **日志**: Serilog

## 快速开始

### 环境要求
- Visual Studio 2022 或更高版本
- .NET 8.0 SDK
- Windows 10/11

### 构建和运行

1. **克隆仓库**
   ```bash
   git clone <repository-url>
   cd DSL2CAPL
   ```

2. **还原NuGet包**
   ```bash
   dotnet restore
   ```

3. **构建解决方案**
   ```bash
   dotnet build
   ```

4. **运行应用程序**
   ```bash
   dotnet run --project src/DSL2CAPL.UI
   ```

5. **运行测试**
   ```bash
   dotnet test
   ```

### 在Visual Studio中打开
1. 打开 `DSL2CAPL.sln` 解决方案文件
2. 设置 `DSL2CAPL.UI` 为启动项目
3. 按 F5 运行

## DSL语法示例

```yaml
# 测试用例元信息
metadata:
  name: "TG01_TC01_1021940"
  description: "Block size handling physical addressing"
  test_group: "TG01"

# 测试环境配置
environment:
  bus_type: "CAN"
  ecu_type: "APP"
  addressing: "PHYSICAL"

# 测试数据定义
data_definitions:
  constants:
    DIAG_REQ_ID: "0x123"
    P2_TIME: 50
  frames:
    session_request: "03 22 F1 86 00 00 00 00"

# 测试步骤
test_steps:
  - step: 1
    description: "To confirm ECU Session"
    action:
      type: "send_and_verify"
      send: "${frames.session_request}"
      expect: "04 62 F1 86 01 00 00 00"
      timeout: "${constants.P2_TIME}"
```

## 开发状态

### 当前版本: v0.1.0 (Alpha) - 阶段1基础架构完成

### ✅ 已完成功能
- **项目架构搭建**: Visual Studio 2022解决方案，5个核心项目
- **核心领域模型**: TestCase, TestStep, DataDefinitions等完整模型
- **WPF用户界面框架**: 基于MVVM的现代化界面，Material Design风格
- **服务层设计**: 文件服务、对话框服务、设置服务等
- **DSL语法设计**: 基于YAML的DSL语法，包含完整示例
- **代码生成示例**: 从DSL生成CAPL代码的完整示例
- **单元测试框架**: xUnit + Moq + FluentAssertions
- **项目文档**: 完整的技术方案和开发计划

### 🔄 开发中功能
- **DSL解析器实现**: 基于ANTLR4的解析器开发
- **CAPL代码生成器**: 模板化代码生成系统
- **基础验证机制**: 语法和语义验证

### 📋 计划功能
- **双向转换引擎**: DSL ↔ CAPL 相互转换
- **AI识别模块**: 自动识别测试用例文档
- **语法高亮和自动补全**: 编辑器增强功能
- **质量验证系统**: 代码质量检查和优化建议

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 文档: [项目文档]

## 致谢

感谢所有为这个项目做出贡献的开发者和测试人员。

---

**注意**: 这是一个正在开发中的项目，某些功能可能尚未完全实现。请查看 [Document/Task.md](Document/Task.md) 了解最新的开发进度。
