{"format": 1, "restore": {"D:\\Software\\agent_DSL2CAPL\\TestConversion.csproj": {}}, "projects": {"D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj", "projectName": "DSL2CAPL.Core", "projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.400\\RuntimeIdentifierGraph.json"}}}, "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj", "projectName": "DSL2CAPL.Generator", "projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj": {"projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[7.0.0, )"}, "Scriban": {"target": "Package", "version": "[5.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.400\\RuntimeIdentifierGraph.json"}}}, "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj", "projectName": "DSL2CAPL.Parser", "projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj": {"projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.400\\RuntimeIdentifierGraph.json"}}}, "D:\\Software\\agent_DSL2CAPL\\TestConversion.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Software\\agent_DSL2CAPL\\TestConversion.csproj", "projectName": "TestConversion", "projectPath": "D:\\Software\\agent_DSL2CAPL\\TestConversion.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Software\\agent_DSL2CAPL\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj": {"projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj"}, "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj": {"projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj"}, "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj": {"projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.400\\RuntimeIdentifierGraph.json"}}}}}