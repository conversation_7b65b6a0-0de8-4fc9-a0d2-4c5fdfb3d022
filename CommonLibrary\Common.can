/*
 * Common Library Main Include File
 * DSL2CAPL 公共库主包含文件
 * 
 * Purpose: 统一包含所有公共库文件，提供完整的测试开发支持
 * Author: DSL2CAPL Generator
 * Version: 1.0
 * Created: 2025-07-30
 * 
 * Usage: 在生成的CAPL文件中包含此文件即可使用所有公共功能
 *        #include "Common.can"
 */

#ifndef COMMON_CAN
#define COMMON_CAN

// 包含所有公共库文件
#include "TestFramework.can"
#include "DiagnosticCommon.can"
#include "MessageHandlers.can"
#include "UtilityFunctions.can"

/*
 * 库版本信息
 */
const char COMMON_LIBRARY_VERSION[] = "1.0.0";
const char COMMON_LIBRARY_BUILD_DATE[] = "2025-07-30";

/*
 * 库初始化函数
 * 在测试开始前调用，初始化所有公共库
 */
void CommonLibrary_Initialize()
{
  write("=== DSL2CAPL Common Library ===");
  write("Version: %s", COMMON_LIBRARY_VERSION);
  write("Build Date: %s", COMMON_LIBRARY_BUILD_DATE);
  write("===============================");
  
  // 初始化测试框架
  TestFramework_Configure(1, 0); // 详细日志，不在首次失败时停止
  
  // 初始化诊断变量
  gDiagResponseReceived = 0;
  gDiagConsecutiveFrameCount = 0;
  gDiagMessageComplete = 0;
  gDiagReceivedLength = 0;
  
  write("Common Library initialized successfully");
}

/*
 * 库清理函数
 * 在测试结束后调用，清理资源
 */
void CommonLibrary_Cleanup()
{
  write("Common Library cleanup completed");
}

/*
 * 获取库版本信息
 * @return: 版本字符串
 */
char* CommonLibrary_GetVersion()
{
  return COMMON_LIBRARY_VERSION;
}

/*
 * 检查库兼容性
 * @param requiredVersion: 需要的最低版本
 * @return: 1=兼容, 0=不兼容
 */
int CommonLibrary_CheckCompatibility(char requiredVersion[])
{
  // 简化的版本检查
  return 1; // 假设总是兼容
}

/*
 * 快速诊断测试函数
 * 提供最常用的诊断测试模式
 */

/*
 * 快速Session切换测试
 * @param targetSession: 目标Session (1=Default, 2=Programming, 3=Extended)
 * @return: 1=成功, 0=失败
 */
int QuickTest_ChangeSession(byte targetSession)
{
  char sessionName[32];
  
  switch (targetSession)
  {
    case 1: strcpy(sessionName, "Default"); break;
    case 2: strcpy(sessionName, "Programming"); break;
    case 3: strcpy(sessionName, "Extended"); break;
    default: strcpy(sessionName, "Unknown"); break;
  }
  
  write("Quick Test: Changing to %s Session (0x%02X)", sessionName, targetSession);
  
  return DiagCommon_ChangeSession(targetSession);
}

/*
 * 快速DID读取测试
 * @param did: 数据标识符
 * @param expectedLength: 期望的数据长度 (0=不检查长度)
 * @return: 实际读取的数据长度，失败返回0
 */
int QuickTest_ReadDID(word did, int expectedLength)
{
  byte readData[256];
  int actualLength;
  
  write("Quick Test: Reading DID 0x%04X", did);
  
  actualLength = DiagCommon_ReadDataByIdentifier(did, readData, sizeof(readData));
  
  if (actualLength > 0)
  {
    write("DID 0x%04X read successfully, length: %d bytes", did, actualLength);
    
    if (expectedLength > 0 && actualLength != expectedLength)
    {
      write("Warning: Expected length %d, got %d", expectedLength, actualLength);
    }
    
    return actualLength;
  }
  else
  {
    write("Failed to read DID 0x%04X", did);
    return 0;
  }
}

/*
 * 快速多帧传输测试
 * @param reqData: 请求数据
 * @param reqLen: 请求长度
 * @param blockSize: 流控制块大小
 * @return: 1=成功, 0=失败
 */
int QuickTest_MultiFrameTransfer(byte reqData[], int reqLen, byte blockSize)
{
  int result;
  
  write("Quick Test: Multi-frame transfer with BS=%d", blockSize);
  
  // 发送请求
  result = DiagCommon_SendAndVerifyPattern(reqData, reqLen, NULL, NULL, 0, 150);
  if (!result)
  {
    write("Failed to send multi-frame request");
    return 0;
  }
  
  testWaitForTimeout(50);
  
  // 执行流控制
  result = DiagCommon_FlowControlSequence(0x30, blockSize, 0x00, blockSize, "2* ** ** ** ** ** ** **");
  if (!result)
  {
    write("Failed to execute flow control");
    return 0;
  }
  
  write("Multi-frame transfer completed successfully");
  return 1;
}

/*
 * 快速Block Size循环测试
 * @param reqData: 基础请求数据
 * @param reqLen: 请求长度
 * @param startBS: 起始Block Size
 * @param endBS: 结束Block Size
 * @return: 1=成功, 0=失败
 */
int QuickTest_BlockSizeLoop(byte reqData[], int reqLen, int startBS, int endBS)
{
  write("Quick Test: Block Size loop test (BS %d-%d)", startBS, endBS);
  
  return DiagCommon_LoopFlowControlTest(reqData, reqLen, startBS, endBS);
}

/*
 * 调试辅助函数
 */

/*
 * 打印消息内容
 * @param msg: 消息
 * @param description: 描述
 */
void Debug_PrintMessage(message msg, char description[])
{
  int i;
  
  write("=== %s ===", description);
  write("ID: 0x%03X, DLC: %d", msg.id, msg.dlc);
  write("Data: ", "");
  
  for (i = 0; i < msg.dlc; i++)
  {
    write("%02X ", msg.byte(i));
  }
  write("");
}

/*
 * 打印字节数组
 * @param data: 数据数组
 * @param length: 数据长度
 * @param description: 描述
 */
void Debug_PrintByteArray(byte data[], int length, char description[])
{
  int i;
  
  write("=== %s ===", description);
  write("Length: %d bytes", length);
  write("Data: ", "");
  
  for (i = 0; i < length; i++)
  {
    write("%02X ", data[i]);
    if ((i + 1) % 16 == 0) write(""); // 每16字节换行
  }
  if (length % 16 != 0) write("");
}

/*
 * 性能监控
 */
float gPerformanceStartTime = 0;

/*
 * 开始性能监控
 */
void Performance_Start()
{
  gPerformanceStartTime = timeNowFloat() / 100.0;
}

/*
 * 结束性能监控并报告
 * @param operation: 操作描述
 * @return: 耗时(秒)
 */
float Performance_End(char operation[])
{
  float endTime = timeNowFloat() / 100.0;
  float duration = endTime - gPerformanceStartTime;
  
  write("Performance: %s took %.3f seconds", operation, duration);
  
  return duration;
}

/*
 * 内存使用情况报告
 * 简化版本，实际CAPL环境中可能需要不同的实现
 */
void Debug_MemoryUsage()
{
  write("=== Memory Usage ===");
  write("Test Log Length: %d bytes", gTestLogLength);
  write("Diagnostic Buffer: %d bytes", gDiagReceivedLength);
  write("===================");
}

#endif // COMMON_CAN
