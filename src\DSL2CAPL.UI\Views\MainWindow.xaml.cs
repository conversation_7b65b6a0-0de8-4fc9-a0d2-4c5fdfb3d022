using DSL2CAPL.UI.ViewModels;
using System.Windows;
using System.Windows.Controls;

namespace DSL2CAPL.UI.Views;

/// <summary>
/// MainWindow.xaml 的交互逻辑
/// </summary>
public partial class MainWindow : Window
{
    /// <summary>
    /// 初始化主窗口
    /// </summary>
    /// <param name="viewModel">主窗口视图模型</param>
    public MainWindow(MainWindowViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
        
        // 设置窗口图标
        try
        {
            Icon = new System.Windows.Media.Imaging.BitmapImage(
                new Uri("pack://application:,,,/Resources/app.ico"));
        }
        catch
        {
            // 如果图标文件不存在，忽略错误
        }

        // 订阅窗口事件
        Loaded += MainWindow_Loaded;
        Closing += MainWindow_Closing;
    }

    /// <summary>
    /// 窗口加载完成事件
    /// </summary>
    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            viewModel.OnWindowLoaded();
        }
    }

    /// <summary>
    /// 窗口关闭事件
    /// </summary>
    private void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            if (!viewModel.OnWindowClosing())
            {
                e.Cancel = true;
            }
        }
    }

    // DSL编辑器相关事件处理已移除，使用标准TextBox
}
