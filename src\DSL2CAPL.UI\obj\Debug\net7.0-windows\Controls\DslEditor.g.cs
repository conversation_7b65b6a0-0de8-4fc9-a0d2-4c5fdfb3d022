﻿#pragma checksum "..\..\..\..\Controls\DslEditor.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "660CB149211FD190CA6ACC00A23724E3BA2F8BCC"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DSL2CAPL.UI.Controls {
    
    
    /// <summary>
    /// DslEditor
    /// </summary>
    public partial class DslEditor : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 39 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer LineNumberScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LineNumberPanel;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer EditorScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RichTextBox MainEditor;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup IntelliSensePopup;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SuggestionListBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup ErrorTooltipPopup;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorMessageText;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PositionText;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Controls\DslEditor.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectionText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "7.0.1.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DSL2CAPL.UI;V0.1.0.0;component/controls/dsleditor.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\DslEditor.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "7.0.1.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LineNumberScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 2:
            this.LineNumberPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.EditorScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            
            #line 53 "..\..\..\..\Controls\DslEditor.xaml"
            this.EditorScrollViewer.ScrollChanged += new System.Windows.Controls.ScrollChangedEventHandler(this.MainEditor_ScrollChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.MainEditor = ((System.Windows.Controls.RichTextBox)(target));
            
            #line 56 "..\..\..\..\Controls\DslEditor.xaml"
            this.MainEditor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.MainEditor_TextChanged);
            
            #line default
            #line hidden
            
            #line 57 "..\..\..\..\Controls\DslEditor.xaml"
            this.MainEditor.KeyDown += new System.Windows.Input.KeyEventHandler(this.MainEditor_KeyDown);
            
            #line default
            #line hidden
            
            #line 58 "..\..\..\..\Controls\DslEditor.xaml"
            this.MainEditor.SelectionChanged += new System.Windows.RoutedEventHandler(this.MainEditor_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.IntelliSensePopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 6:
            this.SuggestionListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 75 "..\..\..\..\Controls\DslEditor.xaml"
            this.SuggestionListBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.SuggestionListBox_KeyDown);
            
            #line default
            #line hidden
            
            #line 76 "..\..\..\..\Controls\DslEditor.xaml"
            this.SuggestionListBox.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.SuggestionListBox_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ErrorTooltipPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 8:
            this.ErrorMessageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.PositionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SelectionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

