{"format": 1, "restore": {"D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj": {}}, "projects": {"D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj", "projectName": "DSL2CAPL.Core", "projectPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.101\\RuntimeIdentifierGraph.json"}}}, "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj", "projectName": "DSL2CAPL.Generator", "projectPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\DSL2CAPL.Generator.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Generator\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj": {"projectPath": "D:\\code\\agent_DSL2CAPL\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[7.0.0, )"}, "Scriban": {"target": "Package", "version": "[5.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.101\\RuntimeIdentifierGraph.json"}}}}}