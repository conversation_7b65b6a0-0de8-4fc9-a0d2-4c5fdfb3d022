/*
 * Message Handlers for Diagnostic Common Library
 * 诊断消息处理器
 * 
 * Purpose: 处理诊断响应和连续帧消息
 * Author: DSL2CAPL Generator
 * Version: 1.0
 * Created: 2025-07-30
 */

#ifndef MESSAGE_HANDLERS_CAN
#define MESSAGE_HANDLERS_CAN

/*
 * 诊断响应消息处理器
 * 处理来自ECU的诊断响应
 */
on message DIAG_DEFAULT_RES_ID
{
  gDiagResponseReceived = 1;
  gDiagLastResponse = this;
  
  write("Received diagnostic response: ID=0x%03X, DLC=%d", this.id, this.dlc);
  
  // 检查是否为首帧 (First Frame)
  if ((this.byte(0) & 0xF0) == 0x10)
  {
    int totalLength = ((this.byte(0) & 0x0F) << 8) | this.byte(1);
    write("First Frame received, total length: %d bytes", totalLength);
    
    // 存储首帧数据
    gDiagReceivedLength = this.dlc - 2; // 减去PCI字节
    memcpy(gDiagReceivedData, this.byte(2), gDiagReceivedLength);
    
    // 重置连续帧计数
    gDiagConsecutiveFrameCount = 0;
    gDiagMessageComplete = 0;
  }
  // 检查是否为单帧 (Single Frame)
  else if ((this.byte(0) & 0xF0) == 0x00)
  {
    int dataLength = this.byte(0) & 0x0F;
    write("Single Frame received, data length: %d bytes", dataLength);
    
    // 存储单帧数据
    gDiagReceivedLength = dataLength;
    memcpy(gDiagReceivedData, this.byte(1), dataLength);
    gDiagMessageComplete = 1;
  }
}

/*
 * 连续帧消息处理器
 * 处理多帧传输中的连续帧
 */
on message *
{
  // 检查是否为连续帧 (Consecutive Frame)
  if ((this.id == DIAG_DEFAULT_RES_ID) && ((this.byte(0) & 0xF0) == 0x20))
  {
    int sequenceNumber = this.byte(0) & 0x0F;
    int dataLength = this.dlc - 1; // 减去PCI字节
    
    gDiagConsecutiveFrameCount++;
    write("Received consecutive frame #%d, sequence: %d", gDiagConsecutiveFrameCount, sequenceNumber);
    
    // 存储连续帧数据
    if (gDiagReceivedLength + dataLength < sizeof(gDiagReceivedData))
    {
      memcpy(gDiagReceivedData[gDiagReceivedLength], this.byte(1), dataLength);
      gDiagReceivedLength += dataLength;
    }
    
    // 简化的消息完整性检查
    // 实际实现中需要根据首帧的总长度来判断
    if (gDiagConsecutiveFrameCount >= 5) // 假设5个连续帧后消息完整
    {
      gDiagMessageComplete = 1;
      write("Multi-frame message completed");
    }
  }
}

/*
 * 功能寻址响应处理器
 * 处理功能寻址的响应
 */
on message DIAG_FUNCTIONAL_RES_ID
{
  write("Received functional addressing response: ID=0x%03X", this.id);
  gDiagResponseReceived = 1;
  gDiagLastResponse = this;
}

/*
 * 错误响应处理器
 * 处理负响应 (Negative Response)
 */
void DiagCommon_HandleNegativeResponse(message response)
{
  if (response.dlc >= 3 && response.byte(0) == 0x03 && response.byte(1) == 0x7F)
  {
    byte serviceId = response.byte(2);
    byte nrc = response.byte(3);
    
    write("Negative Response: Service=0x%02X, NRC=0x%02X", serviceId, nrc);
    
    switch (nrc)
    {
      case 0x10:
        write("NRC: General Reject");
        break;
      case 0x11:
        write("NRC: Service Not Supported");
        break;
      case 0x12:
        write("NRC: Sub-Function Not Supported");
        break;
      case 0x13:
        write("NRC: Incorrect Message Length Or Invalid Format");
        break;
      case 0x21:
        write("NRC: Busy Repeat Request");
        break;
      case 0x22:
        write("NRC: Conditions Not Correct");
        break;
      case 0x31:
        write("NRC: Request Out Of Range");
        break;
      case 0x33:
        write("NRC: Security Access Denied");
        break;
      case 0x35:
        write("NRC: Invalid Key");
        break;
      case 0x36:
        write("NRC: Exceed Number Of Attempts");
        break;
      case 0x37:
        write("NRC: Required Time Delay Not Expired");
        break;
      case 0x78:
        write("NRC: Request Correctly Received - Response Pending");
        break;
      default:
        write("NRC: Unknown (0x%02X)", nrc);
        break;
    }
  }
}

/*
 * 诊断会话管理
 * 切换诊断会话
 */
int DiagCommon_ChangeSession(byte sessionType)
{
  byte reqData[] = {0x02, 0x10, sessionType};
  byte expPattern[] = {0x02, 0x50, sessionType};
  byte expMask[] = {0xFF, 0xFF, 0xFF};
  
  write("Changing to session 0x%02X", sessionType);
  
  return DiagCommon_SendAndVerifyPattern(reqData, 3, expPattern, expMask, 3, DIAG_DEFAULT_TIMEOUT);
}

/*
 * 安全访问序列
 * 执行安全访问解锁
 */
int DiagCommon_SecurityAccess(byte level, byte seed[], int seedLen, byte key[], int keyLen)
{
  byte reqSeed[8] = {0x02, 0x27, level};
  byte reqKey[16];
  int i;
  
  // 请求种子
  write("Requesting security seed for level 0x%02X", level);
  if (!DiagCommon_SendAndVerifyPattern(reqSeed, 3, NULL, NULL, 0, DIAG_DEFAULT_TIMEOUT))
  {
    write("Failed to request security seed");
    return 0;
  }
  
  // 构造密钥请求
  reqKey[0] = keyLen + 2;
  reqKey[1] = 0x27;
  reqKey[2] = level + 1;
  for (i = 0; i < keyLen && i < 13; i++)
  {
    reqKey[3 + i] = key[i];
  }
  
  // 发送密钥
  write("Sending security key");
  return DiagCommon_SendAndVerifyPattern(reqKey, keyLen + 3, NULL, NULL, 0, DIAG_DEFAULT_TIMEOUT);
}

/*
 * 通信控制
 * 控制ECU的通信状态
 */
int DiagCommon_CommunicationControl(byte controlType, byte communicationType)
{
  byte reqData[] = {0x03, 0x28, controlType, communicationType};
  byte expPattern[] = {0x02, 0x68, controlType};
  byte expMask[] = {0xFF, 0xFF, 0xFF};
  
  write("Communication control: type=0x%02X, comm=0x%02X", controlType, communicationType);
  
  return DiagCommon_SendAndVerifyPattern(reqData, 4, expPattern, expMask, 3, DIAG_DEFAULT_TIMEOUT);
}

/*
 * ECU复位
 * 执行ECU复位操作
 */
int DiagCommon_ECUReset(byte resetType)
{
  byte reqData[] = {0x02, 0x11, resetType};
  byte expPattern[] = {0x02, 0x51, resetType};
  byte expMask[] = {0xFF, 0xFF, 0xFF};
  
  write("ECU Reset: type=0x%02X", resetType);
  
  return DiagCommon_SendAndVerifyPattern(reqData, 3, expPattern, expMask, 3, DIAG_P2_STAR_TIMEOUT);
}

/*
 * 读取数据标识符
 * 读取指定的DID数据
 */
int DiagCommon_ReadDataByIdentifier(word did, byte outData[], int maxLen)
{
  byte reqData[] = {0x03, 0x22, (did >> 8) & 0xFF, did & 0xFF};
  byte expPattern[] = {0xFF, 0x62, (did >> 8) & 0xFF, did & 0xFF};
  byte expMask[] = {0x00, 0xFF, 0xFF, 0xFF}; // 长度字节忽略
  
  write("Reading DID 0x%04X", did);
  
  int result = DiagCommon_SendAndVerifyPattern(reqData, 4, expPattern, expMask, 4, DIAG_DEFAULT_TIMEOUT);
  
  if (result && gDiagResponseReceived)
  {
    // 复制响应数据 (跳过服务ID和DID)
    int dataLen = gDiagLastResponse.byte(0) - 3; // 总长度减去服务响应和DID
    if (dataLen > 0 && dataLen <= maxLen)
    {
      memcpy(outData, gDiagLastResponse.byte(4), dataLen);
      return dataLen;
    }
  }
  
  return 0;
}

#endif // MESSAGE_HANDLERS_CAN
