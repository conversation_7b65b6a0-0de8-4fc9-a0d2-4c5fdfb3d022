<UserControl x:Class="DSL2CAPL.UI.Controls.DslEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    
    <UserControl.Resources>
        <Style x:Key="LineNumberStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas, Courier New, monospace" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="Foreground" Value="Gray" />
            <Setter Property="Background" Value="LightGray" />
            <Setter Property="Padding" Value="5,0" />
            <Setter Property="TextAlignment" Value="Right" />
        </Style>
        
        <Style x:Key="EditorStyle" TargetType="RichTextBox">
            <Setter Property="FontFamily" Value="Consolas, Courier New, monospace" />
            <Setter Property="FontSize" Value="12" />
            <Setter Property="AcceptsReturn" Value="True" />
            <Setter Property="AcceptsTab" Value="True" />
            <Setter Property="VerticalScrollBarVisibility" Value="Disabled" />
            <Setter Property="HorizontalScrollBarVisibility" Value="Disabled" />
            <Setter Property="Background" Value="White" />
            <Setter Property="BorderThickness" Value="0" />
        </Style>
    </UserControl.Resources>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="50" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        
        <!-- 行号显示 -->
        <Border Grid.Column="0" Background="LightGray" BorderBrush="Gray" BorderThickness="0,0,1,0">
            <ScrollViewer x:Name="LineNumberScrollViewer" 
                          VerticalScrollBarVisibility="Hidden"
                          HorizontalScrollBarVisibility="Hidden">
                <StackPanel x:Name="LineNumberPanel" Orientation="Vertical">
                    <!-- 行号将在代码中动态生成 -->
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- 主编辑器 -->
        <Grid Grid.Column="1">
            <ScrollViewer x:Name="EditorScrollViewer"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Auto"
                          ScrollChanged="MainEditor_ScrollChanged">
                <RichTextBox x:Name="MainEditor"
                             Style="{StaticResource EditorStyle}"
                             TextChanged="MainEditor_TextChanged"
                             KeyDown="MainEditor_KeyDown"
                             SelectionChanged="MainEditor_SelectionChanged" />
            </ScrollViewer>
            
            <!-- 智能补全弹出框 -->
            <Popup x:Name="IntelliSensePopup" 
                   Placement="Bottom"
                   PlacementTarget="{Binding ElementName=MainEditor}"
                   IsOpen="False"
                   StaysOpen="False">
                <Border Background="White" 
                        BorderBrush="Gray" 
                        BorderThickness="1"
                        CornerRadius="3"
                        MaxHeight="200"
                        MinWidth="200">
                    <ListBox x:Name="SuggestionListBox"
                             SelectionMode="Single"
                             KeyDown="SuggestionListBox_KeyDown"
                             MouseDoubleClick="SuggestionListBox_MouseDoubleClick">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📝" Margin="0,0,5,0" />
                                    <TextBlock Text="{Binding}" FontFamily="Consolas" />
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </Border>
            </Popup>
            
            <!-- 错误提示弹出框 -->
            <Popup x:Name="ErrorTooltipPopup"
                   Placement="Mouse"
                   IsOpen="False"
                   StaysOpen="False">
                <Border Background="LightYellow"
                        BorderBrush="Orange"
                        BorderThickness="1"
                        CornerRadius="3"
                        Padding="5">
                    <StackPanel>
                        <TextBlock Text="⚠️ 语法错误" FontWeight="Bold" Foreground="Red" />
                        <TextBlock x:Name="ErrorMessageText" 
                                   Text="错误描述" 
                                   MaxWidth="300"
                                   TextWrapping="Wrap" />
                    </StackPanel>
                </Border>
            </Popup>
        </Grid>
        
        <!-- 状态栏 -->
        <Border Grid.Column="0" Grid.ColumnSpan="2" 
                VerticalAlignment="Bottom"
                Background="LightBlue"
                Height="20"
                Opacity="0.8">
            <StackPanel Orientation="Horizontal" Margin="5,0">
                <TextBlock x:Name="StatusText" 
                           Text="就绪" 
                           FontSize="10" 
                           VerticalAlignment="Center" />
                <TextBlock Text=" | " FontSize="10" VerticalAlignment="Center" />
                <TextBlock x:Name="PositionText" 
                           Text="行: 1, 列: 1" 
                           FontSize="10" 
                           VerticalAlignment="Center" />
                <TextBlock Text=" | " FontSize="10" VerticalAlignment="Center" />
                <TextBlock x:Name="SelectionText" 
                           Text="选择: 0" 
                           FontSize="10" 
                           VerticalAlignment="Center" />
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
