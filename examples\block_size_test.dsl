# Block Size Handling Test Case - TG01_TC01_1021940
# Purpose: Verify ECU handling of Block Size values in physical addressing mode

metadata:
  name: "TG01_TC01_BlockSizeHandling"
  description: "Block size handling physical addressing - Default session"
  author: "DSL2CAPL Generator"
  version: "1.0"
  test_id: "TG01_TC01_1021940"

environment:
  bus_type: "CAN"
  ecu_type: "APP"
  addressing: "PHYSICAL"
  baudrate: 500000
  session: "DEFAULT"

test_steps:
  - step: 1
    description: "确认ECU Session状态"
    action:
      type: "send_and_verify"
      send: "03 22 F1 86 00 00 00 00"
      expect: "04 62 F1 86 ** 00 00 00"
      timeout: 100
      comment: "To confirm ECU Session"

  - step: 2
    description: "发送多帧数据请求"
    action:
      type: "send_and_verify"
      send: "03 22 ED 20 00 00 00 00"
      expect: "1* ** 62 ED 20 ** ** **"
      timeout: 150
      comment: "Request multi-frame data"

  - step: 3
    description: "发送流控制帧(BS=0)并接收连续帧"
    action:
      type: "flow_control_sequence"
      fc_frame: "30 00 00 00 00 00 00 00"
      expect_cf_pattern: "2* ** ** ** ** ** ** **"
      max_frames: 15
      comment: "BS=0, ECU will send all CFs continuously"

  - step: 4
    description: "重新发送多帧数据请求"
    action:
      type: "send_and_verify"
      send: "03 22 ED 20 00 00 00 00"
      expect: "1* ** 62 ED 20 ** ** **"
      timeout: 150

  - step: 5
    description: "发送流控制帧(BS=1)并接收单个连续帧"
    action:
      type: "flow_control_sequence"
      fc_frame: "30 01 00 00 00 00 00 00"
      expect_cf_pattern: "2* ** ** ** ** ** ** **"
      max_frames: 1
      comment: "BS=1, only one CF shall be sent in the block"

  - step: 6
    description: "重复步骤5直到接收完整消息"
    action:
      type: "repeat_flow_control"
      fc_frame: "30 01 00 00 00 00 00 00"
      repeat_until: "complete_message"
      max_iterations: 20

  - step: 7
    description: "测试不同BS值(BS=2到6)"
    action:
      type: "loop_flow_control"
      bs_range: "2-6"
      fc_frame_template: "30 {BS} 00 00 00 00 00 00"
      base_request: "03 22 ED 20 00 00 00 00"

  - step: 8
    description: "最终确认ECU Session状态"
    action:
      type: "send_and_verify"
      send: "03 22 F1 86 00 00 00 00"
      expect: "04 62 F1 86 ** 00 00 00"
      timeout: 100
      comment: "Final ECU Session confirmation"
