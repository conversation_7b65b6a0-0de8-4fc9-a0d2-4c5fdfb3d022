/*
 * DSL2CAPL Utility Functions Library
 * Version: 1.0
 * Description: 通用工具函数库，提供字符串处理、数据转换等功能
 * Author: DSL2CAPL Generator
 * Created: 2025-07-30
 */

#ifndef UTILITY_FUNCTIONS_CAN
#define UTILITY_FUNCTIONS_CAN

variables
{
  // 工具函数模块状态
  int gUtilityInitialized = 0;
  
  // 临时缓冲区
  char gTempBuffer[2000];
  char gHexBuffer[500];
}

/*
 * 工具函数模块初始化
 */
void Utility_Initialize()
{
  if (gUtilityInitialized)
  {
    write("Warning: Utility functions already initialized");
    return;
  }
  
  gUtilityInitialized = 1;
  write("Utility Functions Library initialized");
}

/*
 * 工具函数模块清理
 */
void Utility_Cleanup()
{
  gUtilityInitialized = 0;
  write("Utility Functions Library cleanup completed");
}

/*
 * 字符串转十六进制数组
 * 输入: "03 22 F1 86"
 * 输出: byte数组 {0x03, 0x22, 0xF1, 0x86}
 */
int Utility_StringToHexArray(char hexString[], byte output[], int maxLength)
{
  int i, j, len, count = 0;
  char temp[3];
  
  len = strlen(hexString);
  
  for (i = 0; i < len && count < maxLength; i++)
  {
    // 跳过空格和其他分隔符
    if (hexString[i] == ' ' || hexString[i] == '\t' || hexString[i] == '\n')
      continue;
      
    // 读取两个字符作为一个字节
    if (i + 1 < len)
    {
      temp[0] = hexString[i];
      temp[1] = hexString[i + 1];
      temp[2] = '\0';
      
      output[count] = (byte)strtol(temp, NULL, 16);
      count++;
      i++; // 跳过下一个字符
    }
  }
  
  return count;
}

/*
 * 十六进制数组转字符串
 * 输入: byte数组 {0x03, 0x22, 0xF1, 0x86}
 * 输出: "03 22 F1 86"
 */
void Utility_HexArrayToString(byte data[], int length, char output[])
{
  int i;
  output[0] = '\0';
  
  for (i = 0; i < length; i++)
  {
    if (i > 0)
      strcat(output, " ");
    sprintf(gTempBuffer, "%02X", data[i]);
    strcat(output, gTempBuffer);
  }
}

/*
 * 模式匹配函数
 * 支持通配符: ** (任意字节), 1* (第一个半字节匹配), *2 (第二个半字节匹配)
 */
int Utility_PatternMatch(byte data[], byte pattern[], byte mask[], int length)
{
  int i;
  
  for (i = 0; i < length; i++)
  {
    if (mask[i] == 0x00)
      continue; // 忽略此字节
      
    if (mask[i] == 0xFF)
    {
      if (data[i] != pattern[i])
        return 0; // 完全匹配失败
    }
    else if (mask[i] == 0xF0)
    {
      if ((data[i] & 0xF0) != (pattern[i] & 0xF0))
        return 0; // 高半字节匹配失败
    }
    else if (mask[i] == 0x0F)
    {
      if ((data[i] & 0x0F) != (pattern[i] & 0x0F))
        return 0; // 低半字节匹配失败
    }
  }
  
  return 1; // 匹配成功
}

/*
 * 解析DSL模式字符串
 * 输入: "04 62 F1 86 ** 00 00 00"
 * 输出: pattern数组和mask数组
 */
int Utility_ParseDslPattern(char dslPattern[], byte pattern[], byte mask[], int maxLength)
{
  char tokens[50][10];
  int tokenCount = 0;
  int i, j, len;
  char temp[10];
  
  // 分割字符串
  len = strlen(dslPattern);
  j = 0;
  
  for (i = 0; i <= len; i++)
  {
    if (dslPattern[i] == ' ' || dslPattern[i] == '\0')
    {
      if (j > 0)
      {
        temp[j] = '\0';
        strcpy(tokens[tokenCount], temp);
        tokenCount++;
        j = 0;
      }
    }
    else
    {
      temp[j++] = dslPattern[i];
    }
  }
  
  // 解析每个token
  for (i = 0; i < tokenCount && i < maxLength; i++)
  {
    if (strcmp(tokens[i], "**") == 0)
    {
      pattern[i] = 0x00;
      mask[i] = 0x00; // 忽略
    }
    else if (tokens[i][1] == '*')
    {
      // 1*, 2*, 3* 等 - 高半字节匹配
      pattern[i] = (byte)(strtol(tokens[i], NULL, 16) & 0xF0);
      mask[i] = 0xF0;
    }
    else if (tokens[i][0] == '*')
    {
      // *1, *2, *3 等 - 低半字节匹配
      pattern[i] = (byte)(strtol(tokens[i] + 1, NULL, 16) & 0x0F);
      mask[i] = 0x0F;
    }
    else
    {
      // 完全匹配
      pattern[i] = (byte)strtol(tokens[i], NULL, 16);
      mask[i] = 0xFF;
    }
  }
  
  return tokenCount;
}

/*
 * 计算校验和 (简单累加)
 */
byte Utility_CalculateChecksum(byte data[], int length)
{
  int i;
  byte checksum = 0;
  
  for (i = 0; i < length; i++)
  {
    checksum += data[i];
  }
  
  return checksum;
}

/*
 * 延时函数 (毫秒)
 */
void Utility_DelayMs(int milliseconds)
{
  dword startTime = timeNow();
  while ((timeNow() - startTime) < milliseconds)
  {
    // 等待
  }
}

/*
 * 获取当前时间戳字符串
 */
void Utility_GetTimestamp(char output[])
{
  dword now = timeNow();
  sprintf(output, "%d", now);
}

/*
 * 字符串比较 (忽略大小写)
 */
int Utility_StrCmpIgnoreCase(char str1[], char str2[])
{
  int i, len1, len2;
  
  len1 = strlen(str1);
  len2 = strlen(str2);
  
  if (len1 != len2)
    return 1; // 不相等
    
  for (i = 0; i < len1; i++)
  {
    char c1 = str1[i];
    char c2 = str2[i];
    
    // 转换为小写
    if (c1 >= 'A' && c1 <= 'Z')
      c1 += 32;
    if (c2 >= 'A' && c2 <= 'Z')
      c2 += 32;
      
    if (c1 != c2)
      return 1; // 不相等
  }
  
  return 0; // 相等
}

/*
 * 数组复制
 */
void Utility_ArrayCopy(byte source[], byte dest[], int length)
{
  int i;
  for (i = 0; i < length; i++)
  {
    dest[i] = source[i];
  }
}

/*
 * 清空数组
 */
void Utility_ArrayClear(byte array[], int length)
{
  int i;
  for (i = 0; i < length; i++)
  {
    array[i] = 0;
  }
}

#endif // UTILITY_FUNCTIONS_CAN
