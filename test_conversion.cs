using DSL2CAPL.Parser.DSL;
using DSL2CAPL.Generator.CAPL;
using DSL2CAPL.Core.Models;

class Program
{
    static async Task Main(string[] args)
    {
        try
        {
            // 读取DSL文件
            string dslContent = File.ReadAllText("examples/block_size_test.dsl");
            Console.WriteLine("DSL内容读取成功");

            // 创建解析器和生成器
            var parser = new SimpleDslParser();
            var generator = new SimpleCaplGenerator();

            // 解析DSL
            Console.WriteLine("开始解析DSL...");
            var testCase = await parser.ParseAsync(dslContent);
            
            if (testCase == null)
            {
                Console.WriteLine("DSL解析失败");
                return;
            }
            
            Console.WriteLine($"DSL解析成功，包含 {testCase.TestSteps.Count} 个测试步骤");

            // 生成CAPL代码
            Console.WriteLine("开始生成CAPL代码...");
            var caplCode = await generator.GenerateAsync(testCase);
            
            if (string.IsNullOrEmpty(caplCode))
            {
                Console.WriteLine("CAPL代码生成失败");
                return;
            }
            
            Console.WriteLine("CAPL代码生成成功");
            Console.WriteLine($"生成的代码长度: {caplCode.Length} 字符");
            
            // 保存生成的代码
            File.WriteAllText("output/test_conversion_result.can", caplCode);
            Console.WriteLine("CAPL代码已保存到 output/test_conversion_result.can");
            
            Console.WriteLine("转换测试完成，没有出现格式错误！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"转换过程中出现错误: {ex.Message}");
            Console.WriteLine($"错误类型: {ex.GetType().Name}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        }
    }
} 