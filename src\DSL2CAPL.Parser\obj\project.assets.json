{"version": 3, "targets": {"net7.0": {"DSL2CAPL.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v7.0", "compile": {"bin/placeholder/DSL2CAPL.Core.dll": {}}, "runtime": {"bin/placeholder/DSL2CAPL.Core.dll": {}}}}}, "libraries": {"DSL2CAPL.Core/1.0.0": {"type": "project", "path": "../DSL2CAPL.Core/DSL2CAPL.Core.csproj", "msbuildProject": "../DSL2CAPL.Core/DSL2CAPL.Core.csproj"}}, "projectFileDependencyGroups": {"net7.0": ["DSL2CAPL.Core >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj", "projectName": "DSL2CAPL.Parser", "projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\DSL2CAPL.Parser.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Parser\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {"D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj": {"projectPath": "D:\\Software\\agent_DSL2CAPL\\src\\DSL2CAPL.Core\\DSL2CAPL.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.400\\RuntimeIdentifierGraph.json"}}}}