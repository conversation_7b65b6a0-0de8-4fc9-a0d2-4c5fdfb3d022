<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DSL2CAPL.Generator</name>
    </assembly>
    <members>
        <member name="T:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator">
            <summary>
            简化的CAPL代码生成器
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateAsync(DSL2CAPL.Core.Models.TestCase)">
            <summary>
            从测试用例生成CAPL代码
            </summary>
            <param name="testCase">测试用例</param>
            <returns>生成的CAPL代码</returns>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateCaplCode(DSL2CAPL.Core.Models.TestCase)">
            <summary>
            生成CAPL代码
            </summary>
            <param name="testCase">测试用例</param>
            <returns>CAPL代码</returns>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateFileHeader(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestCase)">
            <summary>
            生成文件头注释
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateIncludes(System.Text.StringBuilder)">
            <summary>
            生成includes
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateVariables(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestCase)">
            <summary>
            生成变量声明
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateMainTestFunction(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestCase)">
            <summary>
            生成主测试函数
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateTestStepFunctions(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestCase)">
            <summary>
            生成测试步骤函数
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateStepActionCode(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestStep)">
            <summary>
            生成步骤动作代码
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateSendAndVerifyCode(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestStep,DSL2CAPL.Core.Models.SendAndVerifyAction)">
            <summary>
            生成发送和验证代码
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateWaitCode(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestStep,DSL2CAPL.Core.Models.WaitAction)">
            <summary>
            生成等待代码
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateFlowControlCode(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestStep,DSL2CAPL.Core.Models.FlowControlSequenceAction)">
            <summary>
            生成流控制代码
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateMessageHandlers(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestCase)">
            <summary>
            生成消息处理器
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateHelperFunctions(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestCase)">
            <summary>
            生成辅助函数
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateTestCaseFunctionName(System.String)">
            <summary>
            生成测试用例函数名
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateStepFunctionName(System.String)">
            <summary>
            生成步骤函数名
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.ParseHexString(System.String)">
            <summary>
            解析十六进制字符串为字节数组
            </summary>
            <param name="hexString">十六进制字符串，如"03 22 F1 86"</param>
            <returns>字节列表</returns>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.ParseExpectedResponse(System.String)">
            <summary>
            解析期望响应模式，支持通配符
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GetActionComment(DSL2CAPL.Core.Models.TestAction)">
            <summary>
            获取动作的注释
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateRepeatFlowControlCode(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestStep,DSL2CAPL.Core.Models.RepeatFlowControlAction)">
            <summary>
            生成重复流控制代码
            </summary>
        </member>
        <member name="M:DSL2CAPL.Generator.CAPL.SimpleCaplGenerator.GenerateLoopFlowControlCode(System.Text.StringBuilder,DSL2CAPL.Core.Models.TestStep,DSL2CAPL.Core.Models.LoopFlowControlAction)">
            <summary>
            生成循环流控制代码
            </summary>
        </member>
    </members>
</doc>
